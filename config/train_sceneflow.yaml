wandb: {}
project_name: sceneflow
restore_ckpt: 'checkpoints/sceneflow_plus/60000_plus.pth'
logdir: './checkpoints/sceneflow_plus/'
encoder: 'vitl'
batch_size: 6
train_datasets: ['sceneflow']
lr: 3e-5
wdecay: 1e-5
total_step: 100000
save_frequency: 2000
save_path: ./checkpoints/sceneflow_plus/
val_frequency: 5000
image_size: [512, 896]
train_iters: 22
valid_iters: 32
val_dataset: 'kitti'
corr_implementation: "reg"
corr_levels: 4
corr_radius: 4
n_downsample: 2
n_gru_layers: 3
hidden_dims: [128, 128, 128]
max_disp: 768
saturation_range: [0.7, 1.3]
do_flip: False
spatial_scale: [-0.2, 0.5]
noyjitter: True
num_gpu: 2
seed: 666

# 🔥 自适应特定参数（简化版）
enable_adaptive: true              # 启用自适应控制
adaptive_lambda_budget: 0.05       # 预算损失权重
adaptive_gamma: 0.15               # 自适应学习率
gamma: 0.8                         # 序列损失衰减系数

# 🔥 渐进式训练参数（新增）
progressive_taraining: true         # 启用渐进式训练
progressive_warmup_steps: 5000     # 权重保持全1的步数（预热阶段）
progressive_transition_steps: 10000 # 从全1过渡到网络预测的步数（过渡阶段）
