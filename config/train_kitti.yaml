wandb: {}
project_name: kitti
restore_ckpt: 'checkpoints/sceneflow_plus_192/50000_plus_345_388.pth'  # 从SceneFlow预训练模型开始
# restore_ckpt: "/data2/cjd/mono_fusion/checkpoints/sceneflow.pth"
logdir: './checkpoints/kitti/'
encoder: 'vitl'
batch_size: 12
train_datasets: ['kitti']
lr: 3e-5
wdecay: 1e-5
total_step: 50000
save_frequency: 2000
save_path: ./checkpoints/kitti/
val_frequency: 2000
image_size: [320, 736]
train_iters: 22
valid_iters: 32
val_dataset: 'kitti'
corr_implementation: "reg"
corr_levels: 4
corr_radius: 4
n_downsample: 2
n_gru_layers: 3
hidden_dims: [128, 128, 128]
max_disp: 192
saturation_range: [0.7, 1.3]
do_flip: False
spatial_scale: [-0.2, 0.5]
noyjitter: True
num_gpu: 4
seed: 655
