[2025-07-16 21:41:46,073][root][INFO] - Adding 138 samples from Middlebury 2014
[2025-07-16 21:41:46,078][root][INFO] - Adding 138 samples from Middlebury 2014
[2025-07-16 21:41:46,080][root][INFO] - Adding 138 samples from Middlebury 2014
[2025-07-16 21:41:46,080][root][INFO] - Adding 138 samples from Middlebury 2014
[2025-07-16 21:41:46,094][root][INFO] - Adding 24 samples from Middlebury 2021
[2025-07-16 21:41:46,094][root][INFO] - Adding 24 samples from Middlebury 2021
[2025-07-16 21:41:46,094][root][INFO] - Adding 24 samples from Middlebury 2021
[2025-07-16 21:41:46,094][root][INFO] - Adding 24 samples from Middlebury 2021
[2025-07-16 21:41:49,692][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-07-16 21:41:49,692][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-07-16 21:41:49,707][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-07-16 21:41:49,709][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-07-16 21:41:53,079][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-07-16 21:41:53,080][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-07-16 21:41:53,096][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-07-16 21:41:53,096][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-07-16 21:41:56,667][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-07-16 21:41:56,668][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:41:56,670][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-07-16 21:41:56,671][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:41:56,701][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-07-16 21:41:56,702][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:41:56,706][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-07-16 21:41:56,707][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:42:00,078][root][INFO] - Added 22386 from FlyingThings frames_cleanpass
[2025-07-16 21:42:00,080][root][INFO] - Added 22386 from FlyingThings frames_cleanpass
[2025-07-16 21:42:00,080][root][INFO] - Added 22386 from FlyingThings frames_cleanpass
[2025-07-16 21:42:00,080][root][INFO] - Added 22386 from FlyingThings frames_cleanpass
[2025-07-16 21:42:03,494][root][INFO] - Added 22386 from Monkaa frames_cleanpass
[2025-07-16 21:42:03,495][root][INFO] - Added 22386 from Monkaa frames_cleanpass
[2025-07-16 21:42:03,495][root][INFO] - Added 22386 from Monkaa frames_cleanpass
[2025-07-16 21:42:03,495][root][INFO] - Added 22386 from Monkaa frames_cleanpass
[2025-07-16 21:42:06,856][root][INFO] - Added 22386 from Driving frames_cleanpass
[2025-07-16 21:42:06,857][root][INFO] - Added 22386 from Driving frames_cleanpass
[2025-07-16 21:42:06,857][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:42:06,857][root][INFO] - Added 22386 from Driving frames_cleanpass
[2025-07-16 21:42:06,857][root][INFO] - Added 22386 from Driving frames_cleanpass
[2025-07-16 21:42:06,858][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:42:06,858][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:42:06,859][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:43:27,179][root][INFO] - Adding 157282 samples from CREStereo Dataset
[2025-07-16 21:43:27,180][root][INFO] - Adding 157282 samples from CREStereo Dataset
[2025-07-16 21:43:27,193][root][INFO] - Adding 157282 samples from CREStereo Dataset
[2025-07-16 21:43:27,237][root][INFO] - Adding 157282 samples from CREStereo Dataset
[2025-07-16 21:55:13,488][root][INFO] - Adding 798890 samples from FSD
[2025-07-16 21:55:13,560][dinov2][INFO] - using MLP layer as FFN
[2025-07-16 21:56:06,279][root][INFO] - Adding 798890 samples from FSD
[2025-07-16 21:56:06,279][root][INFO] - Adding 798890 samples from FSD
[2025-07-16 21:56:06,280][root][INFO] - Adding 798890 samples from FSD
[2025-07-16 21:56:06,351][dinov2][INFO] - using MLP layer as FFN
[2025-07-16 21:56:06,353][dinov2][INFO] - using MLP layer as FFN
[2025-07-16 21:56:06,357][dinov2][INFO] - using MLP layer as FFN
