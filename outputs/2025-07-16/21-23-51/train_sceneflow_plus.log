[2025-07-16 21:23:51,610][root][INFO] - Adding 138 samples from Middlebury 2014
[2025-07-16 21:23:51,612][root][INFO] - Adding 138 samples from Middlebury 2014
[2025-07-16 21:23:51,615][root][INFO] - Adding 138 samples from Middlebury 2014
[2025-07-16 21:23:51,615][root][INFO] - Adding 138 samples from Middlebury 2014
[2025-07-16 21:23:51,643][root][INFO] - Adding 24 samples from Middlebury 2021
[2025-07-16 21:23:51,643][root][INFO] - Adding 24 samples from Middlebury 2021
[2025-07-16 21:23:51,643][root][INFO] - Adding 24 samples from Middlebury 2021
[2025-07-16 21:23:51,643][root][INFO] - Adding 24 samples from Middlebury 2021
[2025-07-16 21:23:55,674][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-07-16 21:23:55,674][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-07-16 21:23:55,674][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-07-16 21:23:55,674][root][INFO] - Added 22386 from FlyingThings frames_finalpass
[2025-07-16 21:23:59,224][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-07-16 21:23:59,224][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-07-16 21:23:59,240][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-07-16 21:23:59,240][root][INFO] - Added 22386 from Monkaa frames_finalpass
[2025-07-16 21:24:02,825][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-07-16 21:24:02,825][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-07-16 21:24:02,826][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:24:02,826][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:24:02,835][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-07-16 21:24:02,836][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:24:02,840][root][INFO] - Added 22386 from Driving frames_finalpass
[2025-07-16 21:24:02,841][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:24:06,234][root][INFO] - Added 22386 from FlyingThings frames_cleanpass
[2025-07-16 21:24:06,234][root][INFO] - Added 22386 from FlyingThings frames_cleanpass
[2025-07-16 21:24:06,234][root][INFO] - Added 22386 from FlyingThings frames_cleanpass
[2025-07-16 21:24:06,234][root][INFO] - Added 22386 from FlyingThings frames_cleanpass
[2025-07-16 21:24:09,580][root][INFO] - Added 22386 from Monkaa frames_cleanpass
[2025-07-16 21:24:09,580][root][INFO] - Added 22386 from Monkaa frames_cleanpass
[2025-07-16 21:24:09,580][root][INFO] - Added 22386 from Monkaa frames_cleanpass
[2025-07-16 21:24:09,581][root][INFO] - Added 22386 from Monkaa frames_cleanpass
[2025-07-16 21:24:12,940][root][INFO] - Added 22386 from Driving frames_cleanpass
[2025-07-16 21:24:12,940][root][INFO] - Added 22386 from Driving frames_cleanpass
[2025-07-16 21:24:12,940][root][INFO] - Added 22386 from Driving frames_cleanpass
[2025-07-16 21:24:12,941][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:24:12,941][root][INFO] - Added 22386 from Driving frames_cleanpass
[2025-07-16 21:24:12,941][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:24:12,941][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:24:12,942][root][INFO] - Adding 67158 samples from SceneFlow
[2025-07-16 21:25:33,721][root][INFO] - Adding 157282 samples from CREStereo Dataset
[2025-07-16 21:25:33,726][root][INFO] - Adding 157282 samples from CREStereo Dataset
[2025-07-16 21:25:33,731][root][INFO] - Adding 157282 samples from CREStereo Dataset
[2025-07-16 21:25:33,821][root][INFO] - Adding 157282 samples from CREStereo Dataset
[2025-07-16 21:37:37,438][root][INFO] - Adding 798890 samples from FSD
[2025-07-16 21:37:37,455][root][INFO] - Adding 798890 samples from FSD
[2025-07-16 21:37:37,456][root][INFO] - Adding 798890 samples from FSD
[2025-07-16 21:37:37,457][root][INFO] - Adding 798890 samples from FSD
[2025-07-16 21:37:37,518][dinov2][INFO] - using MLP layer as FFN
[2025-07-16 21:37:37,533][dinov2][INFO] - using MLP layer as FFN
[2025-07-16 21:37:37,534][dinov2][INFO] - using MLP layer as FFN
[2025-07-16 21:37:37,535][dinov2][INFO] - using MLP layer as FFN
