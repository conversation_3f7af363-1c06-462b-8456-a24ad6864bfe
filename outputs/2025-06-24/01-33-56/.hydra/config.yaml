project_name: monster_plus_sceneflow
save_path: ./checkpoints/sceneflow_plus
logdir: ./checkpoints/sceneflow_plus/
seed: 42
num_gpu: 1
dataset: sceneflow
train_datasets:
- sceneflow
val_dataset: kitti
batch_size: 1
max_disp: 192
image_size:
- 320
- 736
spatial_scale:
- -0.2
- 0.5
saturation_range:
- 0.7
- 1.3
do_flip: false
noyjitter: true
encoder: vitl
n_gru_layers: 3
hidden_dims:
- 128
- 128
- 128
corr_implementation: reg
corr_levels: 2
corr_radius: 4
n_downsample: 2
total_step: 10
train_iters: 2
valid_iters: 32
lr: 0.0002
wdecay: 1.0e-05
gamma: 0.8
confidence_alpha: 0.01
restore_ckpt: checkpoint/sceneflow.pth
save_frequency: 1000
val_frequency: 5000
enable_adaptive: false
adaptive_lambda_budget: 0.05
adaptive_gamma: 0.15
progressive_training: false
progressive_warmup_steps: 5000
progressive_transition_steps: 10000
wandb:
  entity: null
  name: sceneflow_training
