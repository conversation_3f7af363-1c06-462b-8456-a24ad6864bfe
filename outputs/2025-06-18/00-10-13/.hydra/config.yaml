wandb: {}
project_name: adaptive_only_training
restore_ckpt: checkpoints/sceneflow/1000.pth
logdir: ./checkpoints/adaptive_only/
encoder: vitl
batch_size: 20
train_datasets:
- sceneflow
lr: 0.0001
wdecay: 1.0e-05
total_step: 10000
save_frequency: 1000
save_path: ./checkpoints/adaptive_only/
val_frequency: 2000
image_size:
- 320
- 736
train_iters: 22
valid_iters: 32
val_dataset: kitti
corr_implementation: reg
corr_levels: 2
corr_radius: 4
n_downsample: 2
n_gru_layers: 3
hidden_dims:
- 128
- 128
- 128
max_disp: 192
saturation_range:
- 0.7
- 1.3
do_flip: false
spatial_scale:
- -0.2
- 0.5
noyjitter: true
num_gpu: 4
seed: 666
enable_adaptive: true
adaptive_lambda_eff: 0.1
adaptive_lambda_budget: 0.08
adaptive_gamma: 0.2
gamma: 0.8
freeze_backbone: true
adaptive_only: true
warmup_steps: 1000
