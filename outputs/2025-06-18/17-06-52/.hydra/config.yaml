wandb: {}
project_name: kitti_adaptive_only
restore_ckpt: checkpoint/kitti.pth
logdir: ./checkpoints/kitti_adaptive_only/
encoder: vitl
batch_size: 20
train_datasets:
- kitti
lr: 1.0e-05
wdecay: 1.0e-05
total_step: 10000
save_frequency: 2000
save_path: ./checkpoints/kitti_adaptive_only/
val_frequency: 2000
image_size:
- 320
- 1280
train_iters: 22
valid_iters: 32
val_dataset: kitti
corr_implementation: reg
corr_levels: 2
corr_radius: 4
n_downsample: 2
n_gru_layers: 3
hidden_dims:
- 128
- 128
- 128
max_disp: 192
saturation_range:
- 0.7
- 1.3
do_flip: false
spatial_scale:
- -0.2
- 0.5
noyjitter: true
num_gpu: 4
seed: 888
enable_adaptive: true
adaptive_gamma: 0.1
adaptive_lambda_budget: 0.05
gamma: 0.8
