wandb: {}
project_name: sceneflow_192
restore_ckpt: checkpoints/sceneflow_plus/58000_plus.pth
logdir: ./checkpoints/sceneflow_plus_192/
encoder: vitl
batch_size: 6
train_datasets:
- sceneflow
lr: 3.0e-05
wdecay: 1.0e-05
total_step: 150000
save_frequency: 2000
save_path: ./checkpoints/sceneflow_plus_192/
val_frequency: 5000
image_size:
- 512
- 928
train_iters: 22
valid_iters: 32
val_dataset: kitti
corr_implementation: reg
corr_levels: 4
corr_radius: 4
n_downsample: 2
n_gru_layers: 3
hidden_dims:
- 128
- 128
- 128
max_disp: 192
saturation_range:
- 0.7
- 1.3
do_flip: false
spatial_scale:
- -0.2
- 0.5
noyjitter: true
num_gpu: 2
seed: 666
enable_adaptive: true
adaptive_lambda_budget: 0.05
adaptive_gamma: 0.15
gamma: 0.8
progressive_taraining: true
progressive_warmup_steps: 5000
progressive_transition_steps: 10000
