import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Dict, List, Optional
import numpy as np

# ---------------------------------------------------------------------------- #
#  1. 核心策略组件：PolicyHeadGlobal - 全局困难度预算预测
# ---------------------------------------------------------------------------- #

class PolicyHeadGlobal(nn.Module):
    """
    全局策略头，用于预测整个场景的平均困难度预算 (mu_target)。
    基于GRU隐藏状态的全局特征，输出一个标量预算值。
    """
    def __init__(self, in_channels: int, hidden_dim: int = 128):
        super().__init__()
        # 使用自适应平均池化来处理任意尺寸的输入特征图
        self.global_pool = nn.AdaptiveAvgPool2d((1, 1))
        self.mlp = nn.Sequential(
            nn.Linear(in_channels, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),  # 添加dropout提高泛化性
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Linear(hidden_dim // 2, 1),
            # 使用Sigmoid将输出约束在[0, 1]范围内
            nn.Sigmoid()
        )

    def forward(self, policy_features: torch.Tensor) -> torch.Tensor:
        """
        Args:
            policy_features: GRU隐藏状态特征图, shape=(B, C, H, W)
        Returns:
            mu_target: 预测的场景平均困难度预算, shape=(B, 1)
        """
        # 全局平均池化
        x = self.global_pool(policy_features)  # (B, C, 1, 1)
        x = x.view(x.size(0), -1)  # (B, C)
        mu_target = self.mlp(x)  # (B, 1)
        return mu_target

# ---------------------------------------------------------------------------- #
#  2. 核心策略组件：PolicyHeadLocal - 局部迭代权重预测
# ---------------------------------------------------------------------------- #

class PolicyHeadLocal(nn.Module):
    """
    局部策略头，用于生成逐像素的迭代权重图 (w)。
    基于GRU隐藏状态的局部特征，输出空间权重图。
    """
    def __init__(self, in_channels: int, hidden_dim: int = 64):
        super().__init__()
        self.conv_block = nn.Sequential(
            nn.Conv2d(in_channels, hidden_dim, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim, hidden_dim // 2, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim // 2, 1, kernel_size=1, padding=0),
            # 使用Sigmoid将权重约束在[0, 1]
            nn.Sigmoid()
        )

    def forward(self, policy_features: torch.Tensor) -> torch.Tensor:
        """
        Args:
            policy_features: GRU隐藏状态特征图, shape=(B, C, H, W)
        Returns:
            weight_map: 逐像素的迭代权重图, shape=(B, 1, H, W)
        """
        weight_map = self.conv_block(policy_features)
        return weight_map

# ---------------------------------------------------------------------------- #
#  3. 多尺度策略特征提取器
# ---------------------------------------------------------------------------- #

class PolicyFeatureExtractor(nn.Module):
    """
    策略特征提取器，从MonSter的GRU隐藏状态中提取用于决策的特征。
    支持多尺度特征融合。
    """
    def __init__(self, hidden_dims: List[int], output_dim: int = 128):
        super().__init__()
        self.hidden_dims = hidden_dims
        self.output_dim = output_dim
        
        # 🔧 修复通道数计算问题：为每个尺度分配固定的通道数
        channels_per_scale = [32, 32, 64]  # 总共128通道
        assert len(channels_per_scale) == len(hidden_dims), f"通道分配数量 {len(channels_per_scale)} 与 hidden_dims 数量 {len(hidden_dims)} 不匹配"
        assert sum(channels_per_scale) == output_dim, f"通道总数 {sum(channels_per_scale)} 与 output_dim {output_dim} 不匹配"
        
        # 多尺度特征处理
        self.feature_convs = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(dim, channels_per_scale[i], kernel_size=3, padding=1),
                nn.BatchNorm2d(channels_per_scale[i]),
                nn.ReLU(inplace=True)
            ) for i, dim in enumerate(hidden_dims)
        ])
        
        # 特征融合
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(output_dim, output_dim, kernel_size=3, padding=1),
            nn.BatchNorm2d(output_dim),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, hidden_states: List[torch.Tensor]) -> torch.Tensor:
        """
        Args:
            hidden_states: MonSter GRU的多尺度隐藏状态列表
        Returns:
            policy_features: 策略决策特征, shape=(B, output_dim, H, W)
        """
        features = []
        target_size = hidden_states[0].shape[2:]  # 使用最高分辨率作为目标尺寸
        
        for i, (hidden_state, conv) in enumerate(zip(hidden_states, self.feature_convs)):
            feat = conv(hidden_state)
            # 上采样到目标尺寸
            if feat.shape[2:] != target_size:
                feat = F.interpolate(feat, size=target_size, mode='bilinear', align_corners=True)
            features.append(feat)
        
        # 拼接并融合特征
        fused_features = torch.cat(features, dim=1)
        policy_features = self.fusion_conv(fused_features)
        
        return policy_features

# ---------------------------------------------------------------------------- #
#  4. 自适应迭代控制器 - 核心控制逻辑
# ---------------------------------------------------------------------------- #

class AdaptiveIterationController(nn.Module):
    """
    自适应迭代控制器，实现你设计的方案C核心逻辑。
    整合了PolicyHeadGlobal、PolicyHeadLocal和损失计算。
    """
    def __init__(self, 
                 hidden_dims: List[int],
                 gamma: float = 0.1,
                 lambda_eff: float = 0.05,
                 enable_adaptive: bool = True):
        super().__init__()
        self.gamma = gamma
        self.lambda_eff = lambda_eff
        self.enable_adaptive = enable_adaptive
        
        # 策略特征提取器
        self.policy_feature_extractor = PolicyFeatureExtractor(hidden_dims, output_dim=128)
        
        # 双头策略网络
        self.policy_head_local = PolicyHeadLocal(in_channels=128)
        self.policy_head_global = PolicyHeadGlobal(in_channels=128)
        
        # 用于监控的指标
        self.register_buffer('iteration_count', torch.zeros(1))
        
    def forward(self, 
                hidden_states: List[torch.Tensor],
                delta_disp: torch.Tensor,
                ground_truth_disp: Optional[torch.Tensor] = None,
                train_mode: bool = True) -> Tuple[torch.Tensor, torch.Tensor, Dict[str, torch.Tensor]]:
        """
        自适应迭代控制的核心前向传播。
        
        Args:
            hidden_states: MonSter GRU的隐藏状态列表
            delta_disp: 当前迭代的视差增量, shape=(B, 1, H, W)
            ground_truth_disp: 视差真值（训练时提供）, shape=(B, H, W)
            train_mode: 是否为训练模式
            
        Returns:
            weighted_delta_disp: 加权后的视差增量
            weight_map: 权重图
            loss_dict: 损失字典
        """
        batch_size = delta_disp.size(0)
        
        if not self.enable_adaptive:
            # 如果未启用自适应，直接返回原始增量
            return delta_disp, torch.ones_like(delta_disp), {}
        
        # 1. 提取策略特征
        policy_features = self.policy_feature_extractor(hidden_states)
        # 2. 预测局部权重和全局预算
        weight_map = self.policy_head_local(policy_features)  # (B, 1, H, W)
        mu_target = self.policy_head_global(policy_features)  # (B, 1)
        
        # 3. 自适应权重控制
        weighted_delta_disp = weight_map * delta_disp
        
        loss_dict = {}
        
        # 4. 训练模式下计算损失
        if train_mode and ground_truth_disp is not None:
            loss_dict = self._compute_adaptive_losses(
                weight_map, mu_target, delta_disp, ground_truth_disp
            )
        
        return weighted_delta_disp, weight_map, loss_dict
    
    def _compute_adaptive_losses(self, 
                               weight_map: torch.Tensor,
                               mu_target: torch.Tensor,
                               delta_disp: torch.Tensor,
                               ground_truth_disp: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算自适应控制的三项损失：精度损失、效率损失、动态预算损失
        """
        # 确保ground_truth_disp有正确的维度
        if ground_truth_disp.dim() == 3:
            ground_truth_disp = ground_truth_disp.unsqueeze(1)  # (B, 1, H, W)
        
        # 1. 精度损失 - 使用L1损失作为基础
        pixel_error = torch.abs(delta_disp - ground_truth_disp)
        L_accuracy = (weight_map * pixel_error).mean()
        
        # 2. 效率损失 - 鼓励稀疏权重
        current_mean_weight = torch.mean(weight_map, dim=[1, 2, 3], keepdim=True)  # (B, 1, 1, 1)
        L_efficiency = self.lambda_eff * current_mean_weight.mean()
        
        # 3. 动态预算损失 - 使全局预算与实际权重对齐
        mu_target_expanded = mu_target.view(-1, 1, 1, 1)  # (B, 1, 1, 1)
        L_budget_dynamic = self.gamma * torch.mean((current_mean_weight - mu_target_expanded) ** 2)
        
        # 4. 总损失
        total_adaptive_loss = L_accuracy + L_efficiency + L_budget_dynamic
        
        return {
            'adaptive_loss_total': total_adaptive_loss,
            'adaptive_loss_accuracy': L_accuracy,
            'adaptive_loss_efficiency': L_efficiency,
            'adaptive_loss_budget': L_budget_dynamic,
            'mean_weight': current_mean_weight.mean(),
            'mean_target_budget': mu_target.mean()
        }

# ---------------------------------------------------------------------------- #
#  5. 辅助函数：权重可视化和分析
# ---------------------------------------------------------------------------- #

def visualize_weight_map(weight_map: torch.Tensor, 
                        save_path: Optional[str] = None) -> np.ndarray:
    """
    可视化权重图的辅助函数
    """
    if isinstance(weight_map, torch.Tensor):
        weight_map = weight_map.detach().cpu().numpy()
    
    # 取第一个batch的权重图
    weight_vis = weight_map[0, 0]  # (H, W)
    
    # 归一化到[0, 255]
    weight_vis = (weight_vis * 255).astype(np.uint8)
    
    if save_path:
        import cv2
        cv2.imwrite(save_path, weight_vis)
    
    return weight_vis

def analyze_weight_statistics(weight_map: torch.Tensor) -> Dict[str, float]:
    """
    分析权重图的统计信息
    """
    weight_flat = weight_map.view(-1)
    return {
        'mean': float(weight_flat.mean()),
        'std': float(weight_flat.std()),
        'min': float(weight_flat.min()),
        'max': float(weight_flat.max()),
        'sparsity': float((weight_flat < 0.1).sum() / len(weight_flat))
    } 