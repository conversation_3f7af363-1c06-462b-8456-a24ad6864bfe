"""
🎯 基于误差指导的自适应权重损失

核心思想：
- 大误差区域 → weight_map = 1 (需要迭代)
- 小误差区域 → weight_map = 0 (不需要迭代)
"""

import torch
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple
import numpy as np


class ErrorGuidedLoss:
    """基于误差指导的权重损失计算器"""
    
    def __init__(self, 
                 error_threshold_percentile: float = 0.7,  # 误差阈值百分位
                 error_smoothing_sigma: float = 1.0,        # 误差平滑参数
                 lambda_error_guidance: float = 0.1,        # 误差指导损失权重
                 use_adaptive_threshold: bool = True):       # 是否使用自适应阈值
        self.error_threshold_percentile = error_threshold_percentile
        self.error_smoothing_sigma = error_smoothing_sigma
        self.lambda_error_guidance = lambda_error_guidance
        self.use_adaptive_threshold = use_adaptive_threshold
    
    def compute_error_target(self, 
                           pred: torch.Tensor, 
                           gt: torch.Tensor, 
                           valid: torch.Tensor) -> <PERSON><PERSON>[torch.Tensor, Dict[str, float]]:
        """
        🎯 计算基于误差的目标权重图
        
        Args:
            pred: 预测视差 [B, 1, H, W]
            gt: 真实视差 [B, 1, H, W]  
            valid: 有效性掩码 [B, 1, H, W]
            
        Returns:
            error_target: 目标权重图 [B, H, W]，值在[0,1]
            error_stats: 误差统计信息
        """
        
        # 1. 计算像素级误差
        pixel_error = torch.abs(pred - gt)  # [B, 1, H, W]
        pixel_error = pixel_error.squeeze(1)  # [B, H, W]
        
        # 2. 应用有效性掩码
        valid_mask = valid.squeeze(1).bool()  # [B, H, W]
        pixel_error = pixel_error * valid_mask.float()
        
        # 3. 计算自适应阈值
        batch_size = pixel_error.shape[0]
        error_targets = []
        error_stats = {
            'mean_error': 0.0,
            'threshold': 0.0,
            'high_error_ratio': 0.0
        }
        
        for b in range(batch_size):
            error_b = pixel_error[b]  # [H, W]
            valid_b = valid_mask[b]   # [H, W]
            
            if valid_b.sum() == 0:
                error_targets.append(torch.zeros_like(error_b))
                continue
            
            # 只考虑有效像素的误差
            valid_errors = error_b[valid_b]
            
            if self.use_adaptive_threshold:
                # 自适应阈值：使用误差分布的百分位点
                threshold = torch.quantile(valid_errors, self.error_threshold_percentile)
            else:
                # 固定阈值：使用误差均值的倍数
                threshold = valid_errors.mean() * 1.5
            
            # 4. 生成目标权重图（软阈值，更平滑）
            error_target_b = torch.sigmoid((error_b - threshold) / self.error_smoothing_sigma)
            
            # 5. 应用有效性掩码
            error_target_b = error_target_b * valid_b.float()
            
            error_targets.append(error_target_b)
            
            # 统计信息
            error_stats['mean_error'] += valid_errors.mean().item()
            error_stats['threshold'] += threshold.item()
            error_stats['high_error_ratio'] += (error_b > threshold).float().mean().item()
        
        # 平均统计信息
        for key in error_stats:
            error_stats[key] /= batch_size
        
        error_target = torch.stack(error_targets, dim=0)  # [B, H, W]
        
        return error_target, error_stats
    
    def compute_error_guidance_loss(self, 
                                  weight_map: torch.Tensor,
                                  error_target: torch.Tensor,
                                  valid: torch.Tensor) -> torch.Tensor:
        """
        🎯 计算误差指导损失
        
        Args:
            weight_map: 预测权重图 [B, H, W]
            error_target: 目标权重图 [B, H, W]
            valid: 有效性掩码 [B, 1, H, W]
            
        Returns:
            guidance_loss: 误差指导损失
        """
        
        # 确保维度匹配
        if valid.dim() == 4:
            valid = valid.squeeze(1)  # [B, H, W]
        
        valid_mask = valid.bool()
        
        # MSE损失（平滑）
        mse_loss = F.mse_loss(weight_map, error_target, reduction='none')  # [B, H, W]
        
        # 只计算有效像素的损失
        if valid_mask.sum() > 0:
            guidance_loss = mse_loss[valid_mask].mean()
        else:
            guidance_loss = torch.tensor(0.0, device=weight_map.device)
        
        return self.lambda_error_guidance * guidance_loss


def create_error_guided_loss(config: dict) -> ErrorGuidedLoss:
    """工厂函数：创建误差指导损失计算器"""
    return ErrorGuidedLoss(
        error_threshold_percentile=config.get('error_threshold_percentile', 0.7),
        error_smoothing_sigma=config.get('error_smoothing_sigma', 1.0),
        lambda_error_guidance=config.get('lambda_error_guidance', 0.1),
        use_adaptive_threshold=config.get('use_adaptive_threshold', True)
    )


if __name__ == "__main__":
    # 🧪 简单测试
    print("🔥 ErrorGuidedLoss 测试")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    batch_size, height, width = 2, 32, 64
    
    # 测试数据
    pred = torch.randn(batch_size, 1, height, width).to(device) * 20
    gt = torch.randn(batch_size, 1, height, width).to(device) * 20
    valid = torch.ones(batch_size, 1, height, width).to(device)
    weight_map = torch.rand(batch_size, height, width).to(device)
    
    # 创建误差指导损失计算器
    error_loss = create_error_guided_loss({})
    
    # 计算误差目标
    error_target, error_stats = error_loss.compute_error_target(pred, gt, valid)
    
    print(f"✅ 误差目标计算完成:")
    print(f"  平均误差: {error_stats['mean_error']:.4f}")
    print(f"  阈值: {error_stats['threshold']:.4f}")
    print(f"  高误差区域比例: {error_stats['high_error_ratio']:.4f}")
    
    # 计算指导损失
    guidance_loss = error_loss.compute_error_guidance_loss(weight_map, error_target, valid)
    print(f"  误差指导损失: {guidance_loss.item():.6f}")
    
    print("🎯 测试完成！") 