import torch
import torch.nn as nn
import torch.nn.functional as F
from core.update import BasicMultiUpdateBlock, BasicMultiUpdateBlock_mix2
from core.geometry import Combined_Geo_Encoding_Volume
from core.submodule import *
from core.refinement import REMP
from core.warp import disp_warp
import matplotlib.pyplot as plt

try:
    autocast = torch.cuda.amp.autocast
except:
    class autocast:
        def __init__(self, enabled):
            pass
        def __enter__(self):
            pass
        def __exit__(self, *args):
            pass
import sys
sys.path.append('./Depth-Anything-V2-list3')
from depth_anything_v2.dpt import DepthAnythingV2, DepthAnythingV2_decoder


def compute_scale_shift(monocular_depth, gt_depth, mask=None):
    """计算单目深度和真值深度之间的scale和shift"""
    flattened_depth_maps = monocular_depth.clone().view(-1).contiguous()
    sorted_depth_maps, _ = torch.sort(flattened_depth_maps)
    percentile_10_index = int(0.2 * len(sorted_depth_maps))
    threshold_10_percent = sorted_depth_maps[percentile_10_index]

    if mask is None:
        mask = (gt_depth > 0) & (monocular_depth > 1e-2) & (monocular_depth > threshold_10_percent)
    
    monocular_depth_flat = monocular_depth[mask]
    gt_depth_flat = gt_depth[mask]
    
    X = torch.stack([monocular_depth_flat, torch.ones_like(monocular_depth_flat)], dim=1)
    y = gt_depth_flat
    
    A = torch.matmul(X.t(), X) + 1e-6 * torch.eye(2, device=X.device)
    b = torch.matmul(X.t(), y)
    params = torch.linalg.solve(A, b)
    
    return params[0].item(), params[1].item()


class AdaptiveController(nn.Module):
    """精简版自适应控制器 - 专注核心功能"""
    
    def __init__(self, hidden_dims=[128, 128, 128], gamma=0.1, lambda_eff=0.05):
        super().__init__()
        
        # 🔥 多源特征融合器
        # 网络状态特征处理
        self.net_state_processor = nn.Sequential(
            nn.Conv2d(hidden_dims[2], 64, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 32, 3, padding=1),
            nn.ReLU(inplace=True)
        )
        
        # 几何特征处理
        self.geo_feat_processor = nn.Sequential(
            nn.Conv2d(hidden_dims[2], 32, 3, padding=1), 
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 16, 3, padding=1),
            nn.ReLU(inplace=True)
        )
        
        # 🔥 预定义几何特征通道适配器，避免动态创建导致的权重不一致
        # geo_feat的通道数来自Combined_Geo_Encoding_Volume：
        # 大约是 (geo_volume_channels + init_corr_channels) * num_levels * 2
        # 根据实际情况，geo_feat通常是162维，设置稍大的值以兼容
        self.geo_channel_adapter = nn.Conv2d(162, hidden_dims[2], 1)  # 162是geo_feat的实际通道数
        
        # 光流特征处理
        self.flaw_processor = nn.Sequential(
            nn.Conv2d(96, 32, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 16, 3, padding=1),
            nn.ReLU(inplace=True)
        )
        
        # 视差增量特征处理
        self.delta_disp_processor = nn.Sequential(
            nn.Conv2d(1, 8, 7, padding=3),
            nn.ReLU(inplace=True),
            nn.Conv2d(8, 8, 7, padding=3),
            nn.ReLU(inplace=True)
        )
        
        # 🔥 特征融合层 (32 + 16 + 16 + 8 = 72)
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(72, 64, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 32, 3, padding=1),
            nn.ReLU(inplace=True)
        )
        
        # 🔥 自注意力机制
        self.self_attention = nn.Sequential(
            nn.Conv2d(32, 32, 1),
            nn.Sigmoid()
        )
        
        # 🔥 核心预测头
        # 局部权重预测 - 输出 logits，不使用 Sigmoid
        self.policy_head_local = nn.Sequential(
            nn.Conv2d(32, 16, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 1, 1),
            # 移除 nn.Sigmoid() - 改为在 forward 中动态计算
        )
        
        # 全局预算预测
        self.policy_head_global = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(32, 16),
            nn.ReLU(inplace=True),
            nn.Linear(16, 1),
            nn.Sigmoid()
        )
        
        self.gamma = gamma
        self.lambda_eff = lambda_eff
        
        # 🔥 温度退火参数
        self.T0 = 1.0           # 初始温度
        self.T_min = 0.3        # 最小温度
        self.total_steps = 60000  # 总训练步数（可通过参数传入）
        
        # 🔥 注册 buffer 用于记录全局步数
        self.register_buffer('global_step', torch.tensor(0))
        
        # 🔥 初始化权重，使weight_map初始输出接近1但有一定变化性
        self._init_weight_map_to_ones()
    
    def _init_weight_map_to_ones(self):
        """初始化policy_head_local，使weight_map初始输出接近1但有一定变化性"""
        
        # 🔥 找到最后一个Conv2d层（不是Sigmoid）
        conv_layers = []
        for i, layer in enumerate(self.policy_head_local):
            if isinstance(layer, nn.Conv2d):
                conv_layers.append((i, layer))
        
        if conv_layers:
            # 获取最后一个Conv2d层
            final_conv_idx, final_conv = conv_layers[-1]
            
            # 🔥 修改策略：使用较小的权重和合适的偏置
            with torch.no_grad():
                # 不再设置权重为0，而是使用小的随机权重
                nn.init.normal_(final_conv.weight, mean=0.0, std=0.1)
                
                # 偏置设置为使sigmoid输出接近1但不完全是1
                # sigmoid(2.2) ≈ 0.9, sigmoid(3.0) ≈ 0.95
                nn.init.constant_(final_conv.bias, 2.2)
        
        # 🔥 前面的Conv2d层使用标准初始化
        for idx, (layer_idx, layer) in enumerate(conv_layers[:-1]):  # 排除最后一层
            with torch.no_grad():
                # 使用Xavier初始化
                nn.init.xavier_normal_(layer.weight, gain=0.2)
                if layer.bias is not None:
                    nn.init.constant_(layer.bias, 0.0)
                
        print(f"🔥 AdaptiveController权重初始化完成: weight_map将初始输出接近1但保持可训练性")
    
    def inference_weight_map(self, logits, T_inf=0.1):
        """
        🔥 推理阶段硬化函数
        
        Args:
            logits: 原始 logits 输出 [B, H, W]
            T_inf: 推理温度（很小，接近硬化）
            
        Returns:
            硬化的权重图 [B, H, W]，值为 0 或 1
        """
        # 使用很小的温度进行 sigmoid，然后二值化
        soft_weights = torch.sigmoid(logits / T_inf)
        hard_weights = (soft_weights > 0.5).float()
        return hard_weights

    def forward(self, net_state, delta_disp, geo_feat=None, flaw=None):
        """
        精简版前向传播 - 专注核心功能
        
        Args:
            net_state: 网络状态特征 [B, C, H, W]
            delta_disp: 视差增量 [B, 1, H, W] 
            geo_feat: 几何特征 [B, C, H, W] (可选)
            flaw: 光流特征 [B, C, H, W] (可选)
            
        Returns:
            controlled_delta_disp: 控制后的视差增量 [B, 1, H, W]
            info: 包含 'weight_map' 和 'mu_target' 的字典
        """
        
        # 🔥 处理网络状态特征
        net_features = self.net_state_processor(net_state)
        
        # 🔥 处理视差增量特征
        delta_features = self.delta_disp_processor(delta_disp)
        
        # 🔥 收集所有特征
        feature_list = [net_features, delta_features]
        
        # 🔥 处理几何特征 (如果提供)
        if geo_feat is not None:
            # 调整几何特征尺寸以匹配其他特征
            if geo_feat.size(-1) != net_state.size(-1) or geo_feat.size(-2) != net_state.size(-2):
                geo_feat = F.interpolate(geo_feat, size=(net_state.size(-2), net_state.size(-1)), mode='bilinear', align_corners=False)
            
            # 🔥 使用预定义的通道适配器
            if geo_feat.size(1) != net_state.size(1):
                geo_processed = self.geo_channel_adapter(geo_feat)
            else:
                geo_processed = geo_feat
            geo_features = self.geo_feat_processor(geo_processed)
            feature_list.append(geo_features)
        
        # 🔥 处理光流特征 (如果提供)
        if flaw is not None:
            # 调整光流特征尺寸
            if flaw.size(-1) != net_state.size(-1) or flaw.size(-2) != net_state.size(-2):
                flaw = F.interpolate(flaw, size=(net_state.size(-2), net_state.size(-1)), mode='bilinear', align_corners=False)
            flaw_features = self.flaw_processor(flaw)
            feature_list.append(flaw_features)
        
        # 🔥 特征融合
        fused_features = torch.cat(feature_list, dim=1)
        fused_features = self.feature_fusion(fused_features)
        
        # 🔥 自注意力增强
        attention_weights = self.self_attention(fused_features)
        enhanced_features = fused_features * attention_weights
        
        # 🔥 核心预测 - 获取 logits
        logits = self.policy_head_local(enhanced_features).squeeze(1)  # [B, H, W]
        mu_target = self.policy_head_global(enhanced_features)
        
        # 🔥 训练时动态温度退火
        if self.training:
            self.global_step += 1
            # 计算当前温度：从 T0 线性衰减到 T_min
            progress = torch.clamp(self.global_step.float() / self.total_steps, 0, 1)
            T = self.T0 * (1 - progress) + self.T_min * progress
        else:
            # 推理时使用最小温度
            T = self.T_min
        
        # 🔥 使用温度缩放的 Sigmoid
        weight_map = torch.sigmoid(logits / T)
        
        # 🔥 应用最终权重
        controlled_delta_disp = weight_map.unsqueeze(1) * delta_disp  # 恢复 [B, 1, H, W]
        
        # 🎯 返回训练中实际使用的信息，包含 logits 用于二值正则项
        return controlled_delta_disp, {
            'weight_map': weight_map,    # 用于损失计算和可视化 [B, H, W]
            'logits': logits,           # 原始 logits，用于二值正则项 [B, H, W]
            'mu_target': mu_target,     # 用于动态预算损失
            'temperature': T,           # 当前温度，用于监控
            'global_step': self.global_step.item(),  # 当前步数，用于监控
        }


# 复制原版Monster的其他组件 (hourglass, Feat_transfer等)
class hourglass(nn.Module):
    def __init__(self, in_channels):
        super(hourglass, self).__init__()

        self.conv1 = nn.Sequential(BasicConv(in_channels, in_channels*2, is_3d=True, bn=True, relu=True, kernel_size=3,
                                             padding=1, stride=2, dilation=1),
                                   BasicConv(in_channels*2, in_channels*2, is_3d=True, bn=True, relu=True, kernel_size=3,
                                             padding=1, stride=1, dilation=1))
                                    
        self.conv2 = nn.Sequential(BasicConv(in_channels*2, in_channels*4, is_3d=True, bn=True, relu=True, kernel_size=3,
                                             padding=1, stride=2, dilation=1),
                                   BasicConv(in_channels*4, in_channels*4, is_3d=True, bn=True, relu=True, kernel_size=3,
                                             padding=1, stride=1, dilation=1))                             

        self.conv3 = nn.Sequential(BasicConv(in_channels*4, in_channels*6, is_3d=True, bn=True, relu=True, kernel_size=3,
                                             padding=1, stride=2, dilation=1),
                                   BasicConv(in_channels*6, in_channels*6, is_3d=True, bn=True, relu=True, kernel_size=3,
                                             padding=1, stride=1, dilation=1)) 

        self.conv3_up = BasicConv(in_channels*6, in_channels*4, deconv=True, is_3d=True, bn=True,
                                  relu=True, kernel_size=(4, 4, 4), padding=(1, 1, 1), stride=(2, 2, 2))

        self.conv2_up = BasicConv(in_channels*4, in_channels*2, deconv=True, is_3d=True, bn=True,
                                  relu=True, kernel_size=(4, 4, 4), padding=(1, 1, 1), stride=(2, 2, 2))

        self.conv1_up = BasicConv(in_channels*2, 8, deconv=True, is_3d=True, bn=False,
                                  relu=False, kernel_size=(4, 4, 4), padding=(1, 1, 1), stride=(2, 2, 2))

        self.agg_0 = nn.Sequential(BasicConv(in_channels*8, in_channels*4, is_3d=True, kernel_size=1, padding=0, stride=1),
                                   BasicConv(in_channels*4, in_channels*4, is_3d=True, kernel_size=3, padding=1, stride=1),
                                   BasicConv(in_channels*4, in_channels*4, is_3d=True, kernel_size=3, padding=1, stride=1),)

        self.agg_1 = nn.Sequential(BasicConv(in_channels*4, in_channels*2, is_3d=True, kernel_size=1, padding=0, stride=1),
                                   BasicConv(in_channels*2, in_channels*2, is_3d=True, kernel_size=3, padding=1, stride=1),
                                   BasicConv(in_channels*2, in_channels*2, is_3d=True, kernel_size=3, padding=1, stride=1))

        self.feature_att_8 = FeatureAtt(in_channels*2, 64)
        self.feature_att_16 = FeatureAtt(in_channels*4, 192)
        self.feature_att_32 = FeatureAtt(in_channels*6, 160)
        self.feature_att_up_16 = FeatureAtt(in_channels*4, 192)
        self.feature_att_up_8 = FeatureAtt(in_channels*2, 64)

    def forward(self, x, features):
        conv1 = self.conv1(x)
        conv1 = self.feature_att_8(conv1, features[1])

        conv2 = self.conv2(conv1)
        conv2 = self.feature_att_16(conv2, features[2])

        conv3 = self.conv3(conv2)
        conv3 = self.feature_att_32(conv3, features[3])

        conv3_up = self.conv3_up(conv3)
        conv2 = torch.cat((conv3_up, conv2), dim=1)
        conv2 = self.agg_0(conv2)
        conv2 = self.feature_att_up_16(conv2, features[2])

        conv2_up = self.conv2_up(conv2)
        conv1 = torch.cat((conv2_up, conv1), dim=1)
        conv1 = self.agg_1(conv1)
        conv1 = self.feature_att_up_8(conv1, features[1])

        conv = self.conv1_up(conv1)
        return conv


class Feat_transfer_cnet(nn.Module):
    def __init__(self, dim_list, output_dim):
        super(Feat_transfer_cnet, self).__init__()
        self.res_16x = nn.Conv2d(dim_list[0]+192, output_dim, kernel_size=3, padding=1, stride=1)
        self.res_8x = nn.Conv2d(dim_list[0]+96, output_dim, kernel_size=3, padding=1, stride=1)
        self.res_4x = nn.Conv2d(dim_list[0]+48, output_dim, kernel_size=3, padding=1, stride=1)

    def forward(self, features, stem_x_list):
        features_list = []
        feat_16x = self.res_16x(torch.cat((features[2], stem_x_list[0]), 1))
        feat_8x = self.res_8x(torch.cat((features[1], stem_x_list[1]), 1))
        feat_4x = self.res_4x(torch.cat((features[0], stem_x_list[2]), 1))
        features_list.append([feat_4x, feat_4x])
        features_list.append([feat_8x, feat_8x])
        features_list.append([feat_16x, feat_16x])
        return features_list


class Feat_transfer(nn.Module):
    def __init__(self, dim_list):
        super(Feat_transfer, self).__init__()
        self.conv4x = nn.Sequential(
            nn.Conv2d(in_channels=int(48+dim_list[0]), out_channels=48, kernel_size=5, stride=1, padding=2),
            nn.InstanceNorm2d(48), nn.ReLU()
        )
        self.conv8x = nn.Sequential(
            nn.Conv2d(in_channels=int(64+dim_list[0]), out_channels=64, kernel_size=5, stride=1, padding=2),
            nn.InstanceNorm2d(64), nn.ReLU()
        )
        self.conv16x = nn.Sequential(
            nn.Conv2d(in_channels=int(192+dim_list[0]), out_channels=192, kernel_size=5, stride=1, padding=2),
            nn.InstanceNorm2d(192), nn.ReLU()
        )
        self.conv32x = nn.Sequential(
            nn.Conv2d(in_channels=dim_list[0], out_channels=160, kernel_size=3, stride=1, padding=1),
            nn.InstanceNorm2d(160), nn.ReLU()
        )
        self.conv_up_32x = nn.ConvTranspose2d(160, 192, kernel_size=3, padding=1, output_padding=1, stride=2, bias=False)
        self.conv_up_16x = nn.ConvTranspose2d(192, 64, kernel_size=3, padding=1, output_padding=1, stride=2, bias=False)
        self.conv_up_8x = nn.ConvTranspose2d(64, 48, kernel_size=3, padding=1, output_padding=1, stride=2, bias=False)
        
        self.res_16x = nn.Conv2d(dim_list[0], 192, kernel_size=1, padding=0, stride=1)
        self.res_8x = nn.Conv2d(dim_list[0], 64, kernel_size=1, padding=0, stride=1)
        self.res_4x = nn.Conv2d(dim_list[0], 48, kernel_size=1, padding=0, stride=1)

    def forward(self, features):
        features_mono_list = []
        feat_32x = self.conv32x(features[3])
        feat_32x_up = self.conv_up_32x(feat_32x)
        feat_16x = self.conv16x(torch.cat((features[2], feat_32x_up), 1)) + self.res_16x(features[2])
        feat_16x_up = self.conv_up_16x(feat_16x)
        feat_8x = self.conv8x(torch.cat((features[1], feat_16x_up), 1)) + self.res_8x(features[1])
        feat_8x_up = self.conv_up_8x(feat_8x)
        feat_4x = self.conv4x(torch.cat((features[0], feat_8x_up), 1)) + self.res_4x(features[0])
        features_mono_list.append(feat_4x)
        features_mono_list.append(feat_8x)
        features_mono_list.append(feat_16x)
        features_mono_list.append(feat_32x)
        return features_mono_list


class Monster(nn.Module):
    """
    🔥 带自适应控制的Monster模型
    
    基于原版Monster，在迭代循环中插入自适应控制逻辑
    """
    def __init__(self, args):
        super().__init__()
        self.args = args
        
        context_dims = args.hidden_dims

        self.intermediate_layer_idx = {
            'vits': [2, 5, 8, 11],
            'vitb': [2, 5, 8, 11], 
            'vitl': [4, 11, 17, 23], 
            'vitg': [9, 19, 29, 39]
        }
        mono_model_configs = {
            'vits': {'encoder': 'vits', 'features': 64, 'out_channels': [48, 96, 192, 384]},
            'vitb': {'encoder': 'vitb', 'features': 128, 'out_channels': [96, 192, 384, 768]},
            'vitl': {'encoder': 'vitl', 'features': 256, 'out_channels': [256, 512, 1024, 1024]},
            'vitg': {'encoder': 'vitg', 'features': 384, 'out_channels': [1536, 1536, 1536, 1536]}
        }
        dim_list_ = mono_model_configs[self.args.encoder]['features']
        dim_list = [dim_list_]
        
        # 🔥 原版的更新块 - 保持不变
        self.update_block = BasicMultiUpdateBlock(self.args, hidden_dims=args.hidden_dims)
        self.update_block_mix_stereo = BasicMultiUpdateBlock_mix2(self.args, hidden_dims=args.hidden_dims)
        self.update_block_mix_mono = BasicMultiUpdateBlock_mix2(self.args, hidden_dims=args.hidden_dims)

        # 🔥 自适应控制器
        self.adaptive_controller = AdaptiveController(
            hidden_dims=args.hidden_dims,
            gamma=getattr(args, 'adaptive_gamma', 0.1),
            lambda_eff=getattr(args, 'adaptive_lambda_eff', 0.05)
        )
        self.enable_adaptive =  True #getattr(args, 'enable_adaptive', True)

        self.context_zqr_convs = nn.ModuleList([nn.Conv2d(context_dims[i], args.hidden_dims[i]*3, 3, padding=3//2) for i in range(self.args.n_gru_layers)])

        self.feat_transfer = Feat_transfer(dim_list)
        self.feat_transfer_cnet = Feat_transfer_cnet(dim_list, output_dim=args.hidden_dims[0])

        # 原版的其他组件
        self.stem_2 = nn.Sequential(
            BasicConv_IN(3, 32, kernel_size=3, stride=2, padding=1),
            nn.Conv2d(32, 32, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(32), nn.ReLU()
        )
        self.stem_4 = nn.Sequential(
            BasicConv_IN(32, 48, kernel_size=3, stride=2, padding=1),
            nn.Conv2d(48, 48, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(48), nn.ReLU()
        )
        self.stem_8 = nn.Sequential(
            BasicConv_IN(48, 96, kernel_size=3, stride=2, padding=1),
            nn.Conv2d(96, 96, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(96), nn.ReLU()
        )
        self.stem_16 = nn.Sequential(
            BasicConv_IN(96, 192, kernel_size=3, stride=2, padding=1),
            nn.Conv2d(192, 192, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(192), nn.ReLU()
        )

        self.spx = nn.Sequential(nn.ConvTranspose2d(2*32, 9, kernel_size=4, stride=2, padding=1),)
        self.spx_2 = Conv2x_IN(24, 32, True)
        self.spx_4 = nn.Sequential(
            BasicConv_IN(96, 24, kernel_size=3, stride=1, padding=1),
            nn.Conv2d(24, 24, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(24), nn.ReLU()
        )
        self.spx_2_gru = Conv2x(32, 32, True)
        self.spx_gru = nn.Sequential(nn.ConvTranspose2d(2*32, 9, kernel_size=4, stride=2, padding=1),)

        self.conv = BasicConv_IN(96, 96, kernel_size=3, padding=1, stride=1)
        self.desc = nn.Conv2d(96, 96, kernel_size=1, padding=0, stride=1)

        self.corr_stem = BasicConv(8, 8, is_3d=True, kernel_size=3, stride=1, padding=1)
        self.corr_feature_att = FeatureAtt(8, 96)
        self.cost_agg = hourglass(8)
        self.classifier = nn.Conv3d(8, 1, 3, 1, 1, bias=False)

        depth_anything = DepthAnythingV2(**mono_model_configs[args.encoder])
        depth_anything_decoder = DepthAnythingV2_decoder(**mono_model_configs[args.encoder])
        
        self.mono_encoder = depth_anything.pretrained 
        self.mono_decoder = depth_anything.depth_head
        self.feat_decoder = depth_anything_decoder.depth_head
        self.mono_encoder.requires_grad_(False)
        self.mono_decoder.requires_grad_(False)

        del depth_anything, depth_anything_decoder
        self.REMP = REMP()

    def infer_mono(self, image1, image2):
        """单目深度推理 - 与原版相同"""
        height_ori, width_ori = image1.shape[2:]
        resize_image1 = F.interpolate(image1, scale_factor=14 / 16, mode='bilinear', align_corners=True)
        resize_image2 = F.interpolate(image2, scale_factor=14 / 16, mode='bilinear', align_corners=True)

        patch_h, patch_w = resize_image1.shape[-2] // 14, resize_image1.shape[-1] // 14
        features_left_encoder = self.mono_encoder.get_intermediate_layers(resize_image1, self.intermediate_layer_idx[self.args.encoder], return_class_token=True)
        features_right_encoder = self.mono_encoder.get_intermediate_layers(resize_image2, self.intermediate_layer_idx[self.args.encoder], return_class_token=True)
        depth_mono = self.mono_decoder(features_left_encoder, patch_h, patch_w)
        depth_mono = F.relu(depth_mono)
        depth_mono = F.interpolate(depth_mono, size=(height_ori, width_ori), mode='bilinear', align_corners=False)
        features_left_4x, features_left_8x, features_left_16x, features_left_32x = self.feat_decoder(features_left_encoder, patch_h, patch_w)
        features_right_4x, features_right_8x, features_right_16x, features_right_32x = self.feat_decoder(features_right_encoder, patch_h, patch_w)

        return depth_mono, [features_left_4x, features_left_8x, features_left_16x, features_left_32x], [features_right_4x, features_right_8x, features_right_16x, features_right_32x]

    def freeze_bn(self):
        """冻结BatchNorm - 与原版相同"""
        for m in self.modules():
            if isinstance(m, nn.BatchNorm2d):
                m.eval()
            if isinstance(m, nn.SyncBatchNorm):
                m.eval()

    def upsample_disp(self, disp, mask_feat_4, stem_2x):
        """上采样视差 - 与原版相同"""
        xspx = self.spx_2_gru(mask_feat_4, stem_2x)
        spx_pred = self.spx_gru(xspx)
        spx_pred = F.softmax(spx_pred, 1)
        up_disp = context_upsample(disp*4., spx_pred).unsqueeze(1)
        return up_disp

    def forward(self, image1, image2, iters=12, flow_init=None, test_mode=False):
        """
        🔥 带自适应控制的前向传播
        
        核心改动：在迭代循环中插入自适应控制逻辑
        """
        
        # 图像预处理 - 与原版相同
        image1 = (2 * (image1 / 255.0) - 1.0).contiguous()
        image2 = (2 * (image2 / 255.0) - 1.0).contiguous()
        with torch.autocast(device_type='cuda', dtype=torch.float32): 
            depth_mono, features_mono_left, features_mono_right = self.infer_mono(image1, image2)

        scale_factor = 0.25
        size = (int(depth_mono.shape[-2] * scale_factor), int(depth_mono.shape[-1] * scale_factor))

        disp_mono_4x = F.interpolate(depth_mono, size=size, mode='bilinear', align_corners=False)

        features_left = self.feat_transfer(features_mono_left)
        features_right = self.feat_transfer(features_mono_right)
        stem_2x = self.stem_2(image1)
        stem_4x = self.stem_4(stem_2x)
        stem_8x = self.stem_8(stem_4x)
        stem_16x = self.stem_16(stem_8x)
        stem_2y = self.stem_2(image2)
        stem_4y = self.stem_4(stem_2y)

        stem_x_list = [stem_16x, stem_8x, stem_4x]
        features_left[0] = torch.cat((features_left[0], stem_4x), 1)
        features_right[0] = torch.cat((features_right[0], stem_4y), 1)

        match_left = self.desc(self.conv(features_left[0]))
        match_right = self.desc(self.conv(features_right[0]))
        gwc_volume = build_gwc_volume(match_left, match_right, self.args.max_disp//4, 8)
        gwc_volume = self.corr_stem(gwc_volume)
        gwc_volume = self.corr_feature_att(gwc_volume, features_left[0])
        geo_encoding_volume = self.cost_agg(gwc_volume, features_left)

        # 初始视差估计
        prob = F.softmax(self.classifier(geo_encoding_volume).squeeze(1), dim=1)
        init_disp = disparity_regression(prob, self.args.max_disp//4)
        del prob, gwc_volume

        if not test_mode:
            xspx = self.spx_4(features_left[0])
            xspx = self.spx_2(xspx, stem_2x)
            spx_pred = self.spx(xspx)
            spx_pred = F.softmax(spx_pred, 1)

        cnet_list = self.feat_transfer_cnet(features_mono_left, stem_x_list)
        net_list = [torch.tanh(x[0]) for x in cnet_list]
        inp_list = [torch.relu(x[1]) for x in cnet_list]
        inp_list = [torch.relu(x) for x in inp_list]
        inp_list = [list(conv(i).split(split_size=conv.out_channels//3, dim=1)) for i,conv in zip(inp_list, self.context_zqr_convs)]
        net_list_mono = [x.clone() for x in net_list]

        geo_block = Combined_Geo_Encoding_Volume
        geo_fn = geo_block(match_left.float(), match_right.float(), geo_encoding_volume.float(), radius=self.args.corr_radius, num_levels=self.args.corr_levels)
        b, c, h, w = match_left.shape
        coords = torch.arange(w).float().to(match_left.device).reshape(1,1,w,1).repeat(b, h, 1, 1).contiguous()
        disp = init_disp
        disp_preds = []
        adaptive_info_accumulated = {}

        # 🔥 迭代更新 - 核心改动在这里
        for itr in range(iters):
            disp = disp.detach()
            if itr >= int(1):
                disp_mono_4x = disp_mono_4x.detach()
            geo_feat = geo_fn(disp, coords)
            
            # 🔥 前面8次迭代：使用原始更新块，不应用自适应控制
            if itr <= int(iters-8):
                net_list, mask_feat_4, delta_disp = self.update_block(net_list, inp_list, geo_feat, disp, iter16=self.args.n_gru_layers==3, iter08=self.args.n_gru_layers>=2)
                
                # 不应用自适应控制，直接使用原始delta_disp
                final_delta_disp = delta_disp
                adaptive_info = {}
            else:
                # 🔥 后面的迭代：混合更新 + 外部自适应控制
                if itr == int(iters-7):
                    bs, _, _, _ = disp.shape
                    for i in range(bs):
                        with torch.autocast(device_type='cuda', dtype=torch.float32): 
                            scale, shift = compute_scale_shift(disp_mono_4x[i].clone().squeeze(1).to(torch.float32), disp[i].clone().squeeze(1).to(torch.float32))
                        disp_mono_4x[i] = scale * disp_mono_4x[i] + shift
                
                warped_right_mono = disp_warp(features_right[0], disp_mono_4x.clone().to(features_right[0].dtype))[0]  
                flaw_mono = warped_right_mono - features_left[0] 

                warped_right_stereo = disp_warp(features_right[0], disp.clone().to(features_right[0].dtype))[0]  
                flaw_stereo = warped_right_stereo - features_left[0] 
                geo_feat_mono = geo_fn(disp_mono_4x, coords)

                # 🔥 立体匹配更新块
                net_list, mask_feat_4, delta_disp = self.update_block_mix_stereo(net_list, inp_list, flaw_stereo, disp, geo_feat, flaw_mono, disp_mono_4x, geo_feat_mono, iter16=self.args.n_gru_layers==3, iter08=self.args.n_gru_layers>=2)
                
                # 🔥 自适应控制 - 立体匹配
                if self.enable_adaptive:
                    final_delta_disp, adaptive_info = self.adaptive_controller(
                        net_state=net_list[0], 
                        delta_disp=delta_disp, 
                        geo_feat=geo_feat, 
                        flaw=flaw_stereo
                    )
                    adaptive_info_accumulated[f"iter_{itr}_stereo"] = adaptive_info
                else:
                    final_delta_disp = delta_disp
                    adaptive_info = {}

                # 🔥 单目更新块
                net_list_mono, mask_feat_4_mono, delta_disp_mono = self.update_block_mix_mono(net_list_mono, inp_list, flaw_mono, disp_mono_4x, geo_feat_mono, flaw_stereo, disp, geo_feat, iter16=self.args.n_gru_layers==3, iter08=self.args.n_gru_layers>=2)
                
                # 🔥 自适应控制 - 单目
                if self.enable_adaptive:
                    final_delta_disp_mono, adaptive_info_mono = self.adaptive_controller(
                        net_state=net_list_mono[0], 
                        delta_disp=delta_disp_mono, 
                        geo_feat=geo_feat_mono, 
                        flaw=flaw_mono
                    )
                    adaptive_info_accumulated[f"iter_{itr}_mono"] = adaptive_info_mono
                else:
                    final_delta_disp_mono = delta_disp_mono

                disp_mono_4x = disp_mono_4x + final_delta_disp_mono
                disp_mono_4x_up = self.upsample_disp(disp_mono_4x, mask_feat_4_mono, stem_2x)
                disp_preds.append(disp_mono_4x_up)

            # 🔥 应用最终的视差增量
            disp = disp + final_delta_disp
            if test_mode and itr < iters-1:
                continue

            disp_up = self.upsample_disp(disp, mask_feat_4, stem_2x)

            if itr == iters - 1:
                refine_value = self.REMP(disp_mono_4x_up, disp_up, image1, image2)
                disp_up = disp_up + refine_value
            disp_preds.append(disp_up)

        if test_mode:
            return disp_up

        init_disp = context_upsample(init_disp*4., spx_pred.float()).unsqueeze(1)
        return init_disp, disp_preds, depth_mono, adaptive_info_accumulated 