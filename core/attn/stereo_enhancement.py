"""
Monster Plus 立体匹配增强模块

包含用于立体匹配特征增强的注意力机制：
- StereoAttentionBlock: 立体注意力块，结合帧内和极线注意力
- FeatureEnhancer: 多尺度特征增强器
"""

import torch
import torch.nn as nn
from torch.utils.checkpoint import checkpoint

from .rope import RotaryPositionEmbedding2D, PositionGetter
from .block import Block
from .epipolar_block import EpipolarBlock


class StereoAttentionBlock(nn.Module):
    """
    Stereo attention block with frame and epipolar attention.
    
    Features:
    - Frame attention: self-attention within each view
    - Epipolar attention: cross-attention along epipolar lines
    - RoPE position encoding for spatial awareness
    - Register tokens for improved attention dynamics
    - Checkpoint support for memory efficiency
    """
    
    def __init__(self, feature_dim=96, num_heads=4, use_checkpoint=False, 
                 rope_frequency=100, frame_alpha_init=0.1, epipolar_alpha_init=0.1,
                 num_register_tokens=4):
        super().__init__()
        
        self.feature_dim = feature_dim
        self.num_heads = num_heads
        self.use_checkpoint = use_checkpoint
        self.num_register_tokens = num_register_tokens
        
        # Initialize position encoding components
        self.rope = RotaryPositionEmbedding2D(frequency=rope_frequency)
        self.position_getter = PositionGetter()
    
        # Initialize frame and epipolar attention blocks
        block_kwargs = {
            "dim": self.feature_dim,
            "num_heads": self.num_heads,
            "mlp_ratio": 4.0,
            "qkv_bias": True,
            "proj_bias": True,
            "ffn_bias": True,
            "init_values": 0.01,
            "qk_norm": True,
            "rope": self.rope,
        }
        
        self.frame_block = Block(**block_kwargs)
        self.epipolar_block = EpipolarBlock(**block_kwargs, stereo_mode=True)
    
        # Initialize register tokens for left and right views
        # Shape: [1, 2, num_register_tokens, feature_dim]
        # Index 0: left view, Index 1: right view
        if self.num_register_tokens > 0:
            self.register_tokens = nn.Parameter(
                torch.randn(1, 2, self.num_register_tokens, self.feature_dim)
            )
            nn.init.normal_(self.register_tokens, std=1e-6)
        else:
            self.register_tokens = None
    
        # Initialize learnable residual connection weights
        self.frame_alpha = nn.Parameter(torch.tensor(frame_alpha_init))
        self.epipolar_alpha = nn.Parameter(torch.tensor(epipolar_alpha_init))
    
    def forward(self, left_features, right_features):
        """
        Apply stereo attention enhancement to feature pairs.
        
        Args:
            left_features: [B, C, H, W] Left view features
            right_features: [B, C, H, W] Right view features
            
        Returns:
            (enhanced_left, enhanced_right): Enhanced feature pair
        """
        B, C, H, W = left_features.shape
        
        # Prepare position encoding
        pos = self.position_getter(B, H, W, device=left_features.device)
        
        # Add zero positions for register tokens if they exist
        if self.register_tokens is not None:
            # Create zero positions for register tokens
            register_pos = torch.zeros(B, self.num_register_tokens, 2, 
                                     device=pos.device, dtype=pos.dtype)
            # Prepend register positions to spatial positions
            pos = torch.cat([register_pos, pos], dim=1)
        
        # Convert to sequence format (with register tokens if enabled)
        left_seq, right_seq = self._to_sequence_format(left_features, right_features)
        
        # Apply frame attention (self-attention within each view)
        left_seq, right_seq = self._apply_frame_attention(left_seq, right_seq, pos)
        
        # Apply epipolar attention (cross-attention along epipolar lines)
        left_seq, right_seq = self._apply_epipolar_attention(left_seq, right_seq, pos, H, W)
        
        # Convert back to spatial format
        enhanced_left, enhanced_right = self._to_spatial_format(left_seq, right_seq, B, C, H, W)
        
        return enhanced_left, enhanced_right
    
    def _to_sequence_format(self, left_features, right_features):
        """Convert spatial features to sequence format and add register tokens"""
        B, C, H, W = left_features.shape
        left_seq = left_features.view(B, C, H * W).transpose(1, 2)  # [B, H*W, C]
        right_seq = right_features.view(B, C, H * W).transpose(1, 2)  # [B, H*W, C]
        
        # Add register tokens if available
        if self.register_tokens is not None:
            # Expand register tokens to batch size
            left_register = self.register_tokens[:, 0:1, :, :].expand(B, -1, -1, -1)  # [B, 1, num_reg, C]
            right_register = self.register_tokens[:, 1:2, :, :].expand(B, -1, -1, -1)  # [B, 1, num_reg, C]
            
            left_register = left_register.squeeze(1)  # [B, num_reg, C]
            right_register = right_register.squeeze(1)  # [B, num_reg, C]
            
            # Prepend register tokens to sequences
            left_seq = torch.cat([left_register, left_seq], dim=1)  # [B, num_reg+H*W, C]
            right_seq = torch.cat([right_register, right_seq], dim=1)  # [B, num_reg+H*W, C]
        
        return left_seq, right_seq
    
    def _to_spatial_format(self, left_seq, right_seq, B, C, H, W):
        """Convert sequence features back to spatial format, removing register tokens"""
        # Remove register tokens if they were added
        if self.register_tokens is not None:
            left_seq = left_seq[:, self.num_register_tokens:, :]  # Remove first num_register_tokens
            right_seq = right_seq[:, self.num_register_tokens:, :]  # Remove first num_register_tokens
        
        # Convert back to spatial format
        enhanced_left = left_seq.transpose(1, 2).view(B, C, H, W)
        enhanced_right = right_seq.transpose(1, 2).view(B, C, H, W)
        return enhanced_left, enhanced_right
    
    def _apply_frame_attention(self, left_seq, right_seq, pos):
        """Apply frame attention with residual connection"""
        def frame_attention_forward():
            combined_seq = torch.cat([left_seq, right_seq], dim=0)
            pos_combined = torch.cat([pos, pos], dim=0)
            enhanced_combined = self.frame_block(combined_seq, pos=pos_combined)
            
            B = left_seq.shape[0]
            enhanced_left = enhanced_combined[:B]
            enhanced_right = enhanced_combined[B:]
            return enhanced_left, enhanced_right
        
        if self.use_checkpoint and self.training:
            left_enhanced, right_enhanced = checkpoint(frame_attention_forward, use_reentrant=False)
        else:
            left_enhanced, right_enhanced = frame_attention_forward()
        
        # Apply residual connection
        left_seq = left_seq + self.frame_alpha * (left_enhanced - left_seq)
        right_seq = right_seq + self.frame_alpha * (right_enhanced - right_seq)
        
        return left_seq, right_seq
    
    def _apply_epipolar_attention(self, left_seq, right_seq, pos, H, W):
        """Apply epipolar attention with residual connection"""
        def epipolar_attention_forward():
            stereo_seq = torch.cat([left_seq, right_seq], dim=1)
            stereo_pos = torch.cat([pos, pos], dim=1)
            
            # Calculate patch_start_idx based on register tokens
            patch_start_idx = self.num_register_tokens if self.register_tokens is not None else 0
            
            enhanced_stereo = self.epipolar_block(
                stereo_seq, 
                pos=stereo_pos, 
                spatial_shape=(H, W),
                patch_start_idx=patch_start_idx
            )
            
            # Split the enhanced stereo back to left and right
            tokens_per_view = enhanced_stereo.shape[1] // 2
            enhanced_left = enhanced_stereo[:, :tokens_per_view, :]
            enhanced_right = enhanced_stereo[:, tokens_per_view:, :]
            return enhanced_left, enhanced_right
        
        if self.use_checkpoint and self.training:
            left_enhanced, right_enhanced = checkpoint(epipolar_attention_forward, use_reentrant=False)
        else:
            left_enhanced, right_enhanced = epipolar_attention_forward()
        
        # Apply residual connection
        left_seq = left_seq + self.epipolar_alpha * (left_enhanced - left_seq)
        right_seq = right_seq + self.epipolar_alpha * (right_enhanced - right_seq)
        
        return left_seq, right_seq


class FeatureEnhancer(nn.Module):
    """
    简化的多尺度特征增强器
    
    Features:
    - 高效的立体匹配注意力机制
    - 多尺度特征融合
    - Register tokens 用于改善注意力动态
    - 梯度检查点支持
    """
    
    def __init__(self, feature_dim=96, num_layers=3, num_heads=4, use_checkpoint=False, 
                 global_alpha_init=0.2, num_register_tokens=4):
        super().__init__()
        
        self.feature_dim = feature_dim
        self.num_layers = num_layers
        self.num_heads = num_heads
        self.use_checkpoint = use_checkpoint
        self.num_register_tokens = num_register_tokens
        
        # Initialize stereo attention blocks
        self.stereo_blocks = nn.ModuleList([
            StereoAttentionBlock(
                feature_dim=self.feature_dim,  # 🔥 明确传递feature_dim参数
                num_heads=self.num_heads, 
                use_checkpoint=self.use_checkpoint,
                num_register_tokens=self.num_register_tokens
            )
            for _ in range(self.num_layers)
        ])
    
        # Initialize feature compression and adjustment layers
        self.layer_compressors = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(self.feature_dim, self.feature_dim // self.num_layers, 1),
                nn.ReLU(inplace=True)
            ) for _ in range(self.num_layers)
        ])
        
        self.final_adjustment = nn.Sequential(
            nn.Conv2d(self.feature_dim, self.feature_dim, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(self.feature_dim, self.feature_dim, 1)
        )
    
        # Initialize global residual connection weight
        self.global_alpha = nn.Parameter(torch.tensor(global_alpha_init))
    
    def forward(self, left_features, right_features):
        """
        多尺度特征增强前向传播
        
        Args:
            left_features: [B, C, H, W] 左图特征
            right_features: [B, C, H, W] 右图特征
            
        Returns:
            (enhanced_left, enhanced_right): 增强后的特征对
        """
        # 保存每层增强特征
        left_layer_features = []
        right_layer_features = []
        
        # 当前特征
        current_left = left_features
        current_right = right_features
        
        # 多层轻量级增强
        for layer_idx in range(self.num_layers):
            # 应用立体匹配注意力块
            enhanced_left, enhanced_right = self.stereo_blocks[layer_idx](current_left, current_right)
            
            # 更新当前特征
            current_left = enhanced_left
            current_right = enhanced_right
            
            # 保存压缩后的层特征
            left_compressed = self.layer_compressors[layer_idx](enhanced_left)
            right_compressed = self.layer_compressors[layer_idx](enhanced_right)
            
            left_layer_features.append(left_compressed)
            right_layer_features.append(right_compressed)
        
        # 多尺度拼接
        left_concatenated = torch.cat(left_layer_features, dim=1)  # [B, feature_dim, H, W]
        right_concatenated = torch.cat(right_layer_features, dim=1)  # [B, feature_dim, H, W]
        
        # 最终调整
        left_final = self.final_adjustment(left_concatenated)
        right_final = self.final_adjustment(right_concatenated)
        
        # 全局残差连接
        enhanced_left = left_features + self.global_alpha * left_final
        enhanced_right = right_features + self.global_alpha * right_final
        
        return enhanced_left, enhanced_right 