import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple
import math

from .rope import RotaryPositionEmbedding2D, PositionGetter
from .block import Block
from .attention import Attention


class RegionalAttention(nn.Module):
    """
    区域注意力模块 - 对固定大小的区域进行自注意力
    
    核心思想：
    1. 将特征图划分为固定大小的区域
    2. 对每个区域内部进行自注意力
    3. 保持区域间的独立性
    4. 使用现有的Block架构，支持flash attention
    """
    
    def __init__(self, dim, num_heads, region_size, rope=None):
        super().__init__()
        
        self.dim = dim
        self.num_heads = num_heads
        self.region_size = region_size
        
        # 区域内注意力模块 - 复用现有的Block
        self.region_attention = Block(
            dim=dim,
            num_heads=num_heads,
            mlp_ratio=2.0,
            qkv_bias=True,
            proj_bias=True,
            ffn_bias=True,
            init_values=0.01,
            qk_norm=True,
            rope=rope,
            fused_attn=True,  # 启用flash attention
        )
        
        self.position_getter = PositionGetter()
    
    def forward(self, features):
        """
        Args:
            features: [B, C, H, W] 输入特征
            
        Returns:
            enhanced_features: [B, C, H, W] 区域增强后的特征
        """
        B, C, H, W = features.shape
        
        # 计算需要padding到的目标尺寸（向上取整到region_size的倍数）
        H_padded = ((H + self.region_size - 1) // self.region_size) * self.region_size
        W_padded = ((W + self.region_size - 1) // self.region_size) * self.region_size
        
        # 计算实际需要的padding
        pad_h = H_padded - H
        pad_w = W_padded - W
        
        # 如果需要padding，则进行padding
        if pad_h > 0 or pad_w > 0:
            features = F.pad(features, (0, pad_w, 0, pad_h), mode='reflect')
        
        # 计算区域数量
        region_h = H_padded // self.region_size
        region_w = W_padded // self.region_size
        
        # 验证计算正确性
        if region_h == 0 or region_w == 0:
            # 如果特征图太小，直接返回原特征
            return features[:, :, :H, :W]  # 确保返回原始尺寸
        
        # 重塑为区域格式: [B, C, region_h, region_size, region_w, region_size]
        features_regions = features.view(
            B, C, region_h, self.region_size, region_w, self.region_size
        )
        
        # 重新排列: [B, region_h, region_w, C, region_size, region_size]
        features_regions = features_regions.permute(0, 2, 4, 1, 3, 5)
        
        # 重塑为批次处理格式: [B*region_h*region_w, C, region_size*region_size]
        num_regions = region_h * region_w
        features_batch = features_regions.contiguous().view(
            B * num_regions, C, self.region_size * self.region_size
        )
        
        # 转换为序列格式: [B*num_regions, region_size*region_size, C]
        features_seq = features_batch.transpose(1, 2)
        
        # 创建区域内位置编码
        pos = self.position_getter(
            B * num_regions, self.region_size, self.region_size, 
            device=features.device
        )
        
        # 应用区域内注意力
        enhanced_seq = self.region_attention(features_seq, pos=pos)
        
        # 转换回空间格式
        enhanced_batch = enhanced_seq.transpose(1, 2).view(
            B * num_regions, C, self.region_size, self.region_size
        )
        
        # 重塑回区域格式: [B, region_h, region_w, C, region_size, region_size]
        enhanced_regions = enhanced_batch.view(
            B, region_h, region_w, C, self.region_size, self.region_size
        )
        
        # 重新排列: [B, C, region_h, region_size, region_w, region_size]
        enhanced_regions = enhanced_regions.permute(0, 3, 1, 4, 2, 5)
        
        # 重塑为原始空间格式: [B, C, H_padded, W_padded]
        enhanced_features = enhanced_regions.contiguous().view(B, C, H_padded, W_padded)
        
        # 移除填充
        if pad_h > 0 or pad_w > 0:
            enhanced_features = enhanced_features[:, :, :H, :W]
        
        return enhanced_features


class MultiScaleFeatureEnhancer(nn.Module):
    """
    多尺度特征增强器
    
    核心思想：
    1. 输入特征 [B, 4c, H, W] 映射为4个 [B, c, H, W] 特征
    2. 串行处理：全局→16x16→8x8→4x4
    3. 跨尺度信息传递和融合
    4. 最终多尺度特征拼接
    
    Args:
        input_dim: 输入特征维度 (4c)
        base_dim: 基础特征维度 (c)
        num_heads: 注意力头数
        scales: 局部注意力的尺度列表 [16, 8, 4]
        use_checkpoint: 是否使用梯度检查点
        rope_frequency: RoPE位置编码频率
    """
    
    def __init__(self, input_dim, base_dim, num_heads=4, scales=[16, 8, 4], 
                 use_checkpoint=False, rope_frequency=100):
        super().__init__()
        
        # 支持直接在原始维度上处理，无需4倍扩展
        self.input_dim = input_dim
        self.base_dim = base_dim
        self.num_heads = num_heads
        self.scales = scales
        self.use_checkpoint = use_checkpoint
        
        # 初始化RoPE位置编码
        self.rope = RotaryPositionEmbedding2D(frequency=rope_frequency)
        self.position_getter = PositionGetter()
        
        # 1. 输入特征映射头 - 直接在原始维度上处理
        # 为多尺度处理创建4个不同的特征视图
        self.input_projections = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(input_dim, base_dim, 1),
                nn.ReLU(inplace=True)
            ) for _ in range(4)
        ])
        
        # 2. 全局注意力模块
        self.global_attention = Block(
            dim=base_dim,
            num_heads=num_heads,
            mlp_ratio=2.0,
            qkv_bias=True,
            proj_bias=True,
            ffn_bias=True,
            init_values=0.01,
            qk_norm=True,
            rope=self.rope,
            fused_attn=True,  # 启用flash attention
        )
        
        # 3. 局部注意力模块 (为每个尺度创建)
        self.local_attentions = nn.ModuleList([
            RegionalAttention(
                dim=base_dim,
                num_heads=num_heads,
                region_size=scale,
                rope=self.rope
            ) for scale in scales
        ])
        
        # 4. 跨尺度融合卷积 (用于串行连接)
        self.cross_scale_fusions = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(base_dim * 2, base_dim, 1),  # 融合前一尺度和当前输入
                nn.ReLU(inplace=True)
            ) for _ in range(len(scales))
        ])
        
        # 5. 最终输出投影
        self.output_projection = nn.Sequential(
            nn.Conv2d(base_dim * 4, input_dim, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(input_dim, input_dim, 3, padding=1)
        )
        
        # 6. 残差连接权重
        self.alpha = nn.Parameter(torch.tensor(0.1))
    
    def forward(self, left_features, right_features):
        """
        Args:
            left_features: [B, C, H, W] 左图特征
            right_features: [B, C, H, W] 右图特征
            
        Returns:
            enhanced_left: [B, C, H, W] 增强后的左图特征
            enhanced_right: [B, C, H, W] 增强后的右图特征
        """
        # 分别处理左右图特征
        enhanced_left = self._process_single_view(left_features)
        enhanced_right = self._process_single_view(right_features)
        
        return enhanced_left, enhanced_right
    
    def _process_single_view(self, features):
        """
        处理单个视图的特征
        
        Args:
            features: [B, C, H, W] 输入特征
            
        Returns:
            enhanced_features: [B, C, H, W] 增强后的特征
        """
        B, C, H, W = features.shape
        assert C == self.input_dim, f"Expected input dim {self.input_dim}, got {C}"
        
        # 1. 特征映射：为多尺度处理创建4个不同的特征视图
        x0 = self.input_projections[0](features)  # [B, base_dim, H, W] - 全局
        x1 = self.input_projections[1](features)  # [B, base_dim, H, W] - 16x16
        x2 = self.input_projections[2](features)  # [B, base_dim, H, W] - 8x8  
        x3 = self.input_projections[3](features)  # [B, base_dim, H, W] - 4x4
        
        # 2. 全局注意力处理
        x_global = self._apply_global_attention(x0)  # [B, c, H, W]
        
        # 3. 串行局部注意力处理
        current_features = x_global
        scale_features = [x_global]  # 存储每个尺度的特征
        
        for i, (local_attn, fusion_conv, x_input) in enumerate(
            zip(self.local_attentions, self.cross_scale_fusions, [x1, x2, x3])
        ):
            # 跨尺度信息融合
            fused_input = fusion_conv(torch.cat([current_features, x_input], dim=1))
            
            # 应用局部注意力
            if self.use_checkpoint and self.training:
                local_enhanced = torch.utils.checkpoint.checkpoint(
                    local_attn, fused_input, use_reentrant=False
                )
            else:
                local_enhanced = local_attn(fused_input)
            
            # 更新当前特征
            current_features = local_enhanced
            scale_features.append(local_enhanced)
        
        # 4. 多尺度特征拼接
        multi_scale_features = torch.cat(scale_features, dim=1)  # [B, 4*base_dim, H, W]
        
        # 5. 输出投影 - 投影回原始维度
        output_features = self.output_projection(multi_scale_features)
        
        # 6. 残差连接
        enhanced_features = features + self.alpha * (output_features - features)
        
        return enhanced_features
    
    def _apply_global_attention(self, features):
        """应用全局注意力"""
        B, C, H, W = features.shape
        
        # 位置编码
        pos = self.position_getter(B, H, W, device=features.device)
        
        # 转换为序列格式
        feat_seq = features.view(B, C, H * W).transpose(1, 2)  # [B, H*W, C]
        
        # 应用全局注意力
        if self.use_checkpoint and self.training:
            enhanced_seq = torch.utils.checkpoint.checkpoint(
                self.global_attention, feat_seq, pos, use_reentrant=False
            )
        else:
            enhanced_seq = self.global_attention(feat_seq, pos=pos)
        
        # 转换回空间格式
        enhanced_features = enhanced_seq.transpose(1, 2).view(B, C, H, W)
        
        return enhanced_features