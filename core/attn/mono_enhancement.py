"""
单目上下文增强模块
===================

专门增强单目几何结构特征的注意力模块，提升Monster Plus的单目理解能力。

🔥 核心功能：
- 单目上下文特征增强
- 多尺度分层处理
- RoPE位置编码增强空间感知
- 轻量化注意力设计
"""

import torch
import torch.nn as nn
from .rope import RotaryPositionEmbedding2D, PositionGetter
from .block import Block


class MonocularContextEnhancer(nn.Module):
    """
    单目上下文特征增强器
    
    🔥 专门增强inp_list中的单目几何结构特征
    
    设计特点：
    - 2层frame注意力机制
    - 分层处理不同尺度的上下文特征  
    - RoPE位置编码增强空间感知
    - 轻量化设计，专注几何结构理解
    
    Args:
        hidden_dims: List[int] 隐藏层维度列表，对应不同尺度
        num_heads: int 注意力头数，默认4
        rope_frequency: int RoPE频率参数，默认100
    
    Input:
        cnet_list: List[Tensor] 单目上下文特征列表
            每个元素 [B, hidden_dims[i], H_i, W_i]
    
    Output:
        enhanced_cnet_list: List[Tensor] 增强后的特征列表
            维度与输入保持一致
    """
    
    def __init__(self, hidden_dims, num_heads=4, rope_frequency=100):
        super().__init__()
        
        self.hidden_dims = hidden_dims
        self.num_heads = num_heads
        
        # 初始化RoPE位置编码
        self.rope = RotaryPositionEmbedding2D(frequency=rope_frequency)
        self.position_getter = PositionGetter()
        
        # 为每个层级创建2层frame注意力
        self.enhancers = nn.ModuleList()
        for dim in hidden_dims:
            # 2层frame注意力块
            layer_enhancer = nn.ModuleList([
                Block(
                    dim=dim,
                    num_heads=num_heads,
                    mlp_ratio=2.0,  # 轻量化设计
                    qkv_bias=True,
                    proj_bias=True,
                    ffn_bias=True,
                    init_values=0.01,
                    qk_norm=True,
                    rope=self.rope,
                ) for _ in range(2)  # 2层注意力
            ])
            self.enhancers.append(layer_enhancer)
        
        # 残差连接权重
        self.enhancement_alpha = nn.Parameter(torch.tensor(0.1))
    
    def forward(self, cnet_list):
        """
        增强单目上下文特征
        
        使用多层注意力机制增强每个尺度的上下文特征：
        1. 将空间特征转换为序列格式
        2. 应用RoPE位置编码
        3. 通过2层注意力块进行增强
        4. 转换回空间格式并应用残差连接
        
        Args:
            cnet_list: List[Tensor] 单目上下文特征列表
                每个元素 [B, hidden_dims[i], H_i, W_i]
        
        Returns:
            enhanced_cnet_list: List[Tensor] 增强后的特征列表
                维度与输入保持一致，内容得到增强
        """
        enhanced_cnet_list = []
        
        for level_idx, cnet_feat in enumerate(cnet_list):
            B, C, H, W = cnet_feat.shape
            
            # 准备位置编码
            pos = self.position_getter(B, H, W, device=cnet_feat.device)
            
            # 转换为序列格式: [B, C, H, W] -> [B, H*W, C]
            feat_seq = cnet_feat.view(B, C, H * W).transpose(1, 2)
            
            # 应用2层frame注意力
            enhanced_seq = feat_seq
            for attn_block in self.enhancers[level_idx]:
                enhanced_seq = attn_block(enhanced_seq, pos=pos)
            
            # 转换回空间格式: [B, H*W, C] -> [B, C, H, W]
            enhanced_feat = enhanced_seq.transpose(1, 2).view(B, C, H, W)
            
            # 残差连接：原始特征 + α * (增强特征 - 原始特征)
            enhanced_feat = cnet_feat + self.enhancement_alpha * (enhanced_feat - cnet_feat)
            
            enhanced_cnet_list.append(enhanced_feat)
        
        return enhanced_cnet_list


class FrameAttentionEnhancer(nn.Module):
    """
    单特征图Frame注意力增强器
    
    🔥 专门用于增强单个特征图的空间表达能力
    
    设计特点：
    - 2层frame注意力机制
    - RoPE位置编码增强空间感知
    - 轻量化设计，专注空间结构理解
    - 残差连接保持特征稳定性
    
    Args:
        feature_dim: int 特征维度
        num_layers: int 注意力层数，默认2
        num_heads: int 注意力头数，默认4
        rope_frequency: int RoPE频率参数，默认100
    
    Input:
        feat: Tensor [B, C, H, W] 待增强的特征图
    
    Output:
        enhanced_feat: Tensor [B, C, H, W] 增强后的特征图
    """
    
    def __init__(self, feature_dim, num_layers=2, num_heads=4, rope_frequency=100):
        super().__init__()
        
        self.feature_dim = feature_dim
        self.num_layers = num_layers
        self.num_heads = num_heads
        
        # 初始化RoPE位置编码
        self.rope = RotaryPositionEmbedding2D(frequency=rope_frequency)
        self.position_getter = PositionGetter()
        
        # 创建多层frame注意力块
        self.attention_blocks = nn.ModuleList([
            Block(
                dim=feature_dim,
                num_heads=num_heads,
                mlp_ratio=2.0,  # 轻量化设计
                qkv_bias=True,
                proj_bias=True,
                ffn_bias=True,
                init_values=0.01,
                qk_norm=True,
                rope=self.rope,
            ) for _ in range(num_layers)
        ])
        
        # 残差连接权重
        self.enhancement_alpha = nn.Parameter(torch.tensor(0.1))
    
    def forward(self, feat):
        """
        增强单个特征图
        
        使用多层注意力机制增强特征图的空间表达：
        1. 将空间特征转换为序列格式
        2. 应用RoPE位置编码
        3. 通过多层注意力块进行增强
        4. 转换回空间格式并应用残差连接
        
        Args:
            feat: Tensor [B, C, H, W] 待增强的特征图
        
        Returns:
            enhanced_feat: Tensor [B, C, H, W] 增强后的特征图
        """
        B, C, H, W = feat.shape
        
        # 准备位置编码
        pos = self.position_getter(B, H, W, device=feat.device)
        
        # 转换为序列格式: [B, C, H, W] -> [B, H*W, C]
        feat_seq = feat.view(B, C, H * W).transpose(1, 2)
        
        # 应用多层frame注意力
        enhanced_seq = feat_seq
        for attn_block in self.attention_blocks:
            enhanced_seq = attn_block(enhanced_seq, pos=pos)
        
        # 转换回空间格式: [B, H*W, C] -> [B, C, H, W]
        enhanced_feat = enhanced_seq.transpose(1, 2).view(B, C, H, W)
        
        # 残差连接：原始特征 + α * (增强特征 - 原始特征)
        enhanced_feat = feat + self.enhancement_alpha * (enhanced_feat - feat)
        
        return enhanced_feat


__all__ = ['MonocularContextEnhancer', 'FrameAttentionEnhancer'] 