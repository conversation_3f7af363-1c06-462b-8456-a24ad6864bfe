# Copyright (c) Meta Platforms, Inc. and affiliates.
#
# This source code is licensed under the Apache License, Version 2.0
# found in the LICENSE file in the root directory of this source tree.

import torch
import torch.nn as nn
from typing import Optional, Tuple
from .block import Block


class EpipolarBlock(Block):
    """
    A Block with epipolar-constrained attention for stereo matching.
    
    Inherits from VGGT's standard Block but modifies the attention mechanism
    to respect epipolar geometry. When processing concatenated left-right tokens,
    it constrains attention to horizontal scanlines.
    
    This maintains VGGT's elegant Block interface while adding stereo-specific geometry.
    
    Args:
        stereo_mode: Whether to enable epipolar constraint mode
        *args, **kwargs: Arguments passed to parent Block
    """
    
    def __init__(self, *args, stereo_mode: bool = True, **kwargs):
        # Remove stereo-specific kwargs before passing to parent
        self.stereo_mode = stereo_mode
        super().__init__(*args, **kwargs)
        
    def forward(self, x: torch.Tensor, pos: Optional[torch.Tensor] = None, spatial_shape: Optional[Tuple[int, int]] = None, patch_start_idx: int = 0) -> torch.Tensor:
        """
        Forward pass with optional epipolar constraint.
        
        Args:
            x: Input tokens. For stereo mode, expects [B, 2*total_tokens, C] where 
               first total_tokens are left view, second total_tokens are right view.
               total_tokens = num_register_tokens + H*W
            pos: Position embeddings [B, 2*total_tokens, 2] or [B, H*W, 2]
            spatial_shape: (H, W) spatial dimensions in patches. If None, will attempt to infer from patch token count.
            patch_start_idx: Index where patch tokens start (after register tokens)
        
        Returns:
            Updated tokens with same shape as input
            
        Raises:
            ValueError: If spatial dimensions cannot be determined
        """
        if not self.stereo_mode:
            # Standard Block behavior for frame attention
            return super().forward(x, pos)
        
        # Store patch_start_idx for use in _epipolar_attention
        self.patch_start_idx = patch_start_idx
        
        # Validate input shape
        B, total_tokens, C = x.shape
        assert total_tokens % 2 == 0, f"Expected even number of tokens for stereo mode, got {total_tokens}"
        
        # Split left/right tokens
        tokens_per_view = total_tokens // 2  # total tokens per view (register + patch)
        left_tokens = x[:, :tokens_per_view, :]     # [B, total_tokens_per_view, C]
        right_tokens = x[:, tokens_per_view:, :]    # [B, total_tokens_per_view, C]
        
        # Determine spatial dimensions from patch tokens only
        P = tokens_per_view - patch_start_idx  # number of patch tokens per view
        if spatial_shape is not None:
            H, W = spatial_shape
            assert H * W == P, f"Spatial shape {spatial_shape} doesn't match patch token count {P}"
        else:
            # Fallback to inference (may fail for non-square)
            H, W = self._infer_spatial_dimensions(P)
        
        # Apply row-wise cross attention
        # Extract position embeddings for the concatenated tokens if available
        pos_for_epipolar = None
        if pos is not None:
            # pos: [B, 2*tokens_per_view, 2] -> we need this for _epipolar_attention
            pos_for_epipolar = pos
        
        left_updated, right_updated = self._epipolar_attention(
            left_tokens, right_tokens, H, W, pos_for_epipolar
        )
        
        # Concatenate back
        updated_x = torch.cat([left_updated, right_updated], dim=1)
        
        return updated_x
    
    def _infer_spatial_dimensions(self, P: int) -> Tuple[int, int]:
        """
        Infer spatial dimensions from patch count.
        
        Args:
            P: Number of patches
            
        Returns:
            (H, W) spatial dimensions
            
        Raises:
            ValueError: If P is not a perfect square
        """
        H = W = int(P ** 0.5)
        if H * W != P:
            raise ValueError(
                f"Cannot infer square H, W from P={P}. "
                f"Current implementation requires perfect square patch grids. "
                f"Consider setting allow_non_square=True in StereoFormer."
            )
        return H, W
    
    def _epipolar_attention(
        self, 
        left_tokens: torch.Tensor, 
        right_tokens: torch.Tensor, 
        H: int, 
        W: int,
        pos: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Apply attention constrained to epipolar lines (horizontal scanlines).
        Optimized batch processing: reshape to [B*H, 2*W, C] for parallel scanline attention.
        
        Now handles register tokens: register tokens are processed with standard attention,
        while patch tokens are processed with epipolar constraint.
        
        Args:
            left_tokens: Left view tokens [B, total_tokens, C] (register + patch tokens)
            right_tokens: Right view tokens [B, total_tokens, C] (register + patch tokens)
            H: Height in patches
            W: Width in patches
            pos: Position embeddings [B, 2*total_tokens, 2] (from main flow, already concatenated)
            
        Returns:
            Tuple of (updated_left_tokens, updated_right_tokens)
            
        Raises:
            AssertionError: If input shapes are invalid
        """
        B, total_tokens, C = left_tokens.shape
        
        # Extract register tokens and patch tokens
        # Assuming stereo aggregator has self.patch_start_idx defined
        if hasattr(self, 'patch_start_idx'):
            patch_start_idx = self.patch_start_idx
        else:
            # Fallback: assume register tokens don't exist for this block
            patch_start_idx = 0
        
        if patch_start_idx > 0:
            # Split register tokens and patch tokens
            left_register_tokens = left_tokens[:, :patch_start_idx, :]    # [B, num_register_tokens, C]
            left_patch_tokens = left_tokens[:, patch_start_idx:, :]       # [B, P, C]
            right_register_tokens = right_tokens[:, :patch_start_idx, :]  # [B, num_register_tokens, C]
            right_patch_tokens = right_tokens[:, patch_start_idx:, :]     # [B, P, C]
            
            P = left_patch_tokens.shape[1]
        else:
            # No register tokens
            left_register_tokens = None
            right_register_tokens = None
            left_patch_tokens = left_tokens
            right_patch_tokens = right_tokens
            P = total_tokens
        
        # Validate patch token dimensions
        assert P == H * W, f"Patch token count mismatch: {P} != {H}*{W}"
        assert left_patch_tokens.shape == right_patch_tokens.shape, \
            f"Left/right patch tokens must have same shape: {left_patch_tokens.shape} vs {right_patch_tokens.shape}"
        
        # Process register tokens with standard attention (if they exist)
        if left_register_tokens is not None:
            # Concatenate register tokens from both views for cross-view interaction
            combined_register_tokens = torch.cat([left_register_tokens, right_register_tokens], dim=1)  # [B, 2*num_register_tokens, C]
            
            # Create dummy position embeddings for register tokens (set to zero)
            num_register_tokens = left_register_tokens.shape[1]
            register_pos = torch.zeros(B, 2 * num_register_tokens, 2, device=combined_register_tokens.device, dtype=torch.long)
            
            # Apply standard attention to register tokens
            updated_register_tokens = self._apply_block_attention(combined_register_tokens, pos=register_pos)
            
            # Split back into left and right register tokens
            updated_left_register_tokens = updated_register_tokens[:, :num_register_tokens, :]
            updated_right_register_tokens = updated_register_tokens[:, num_register_tokens:, :]
        
        # Process patch tokens with epipolar constraint
        # Reshape to spatial dimensions: [B, H, W, C]
        left_spatial = left_patch_tokens.view(B, H, W, C)
        right_spatial = right_patch_tokens.view(B, H, W, C)
        
        # Concatenate left and right along width dimension: [B, H, 2*W, C]
        # More memory efficient than creating separate copies
        combined_spatial = torch.cat([left_spatial, right_spatial], dim=2)
        
        # Reshape for batch processing all scanlines: [B*H, 2*W, C]
        # This treats each scanline as an independent sequence in the batch
        combined_rows = combined_spatial.view(B * H, 2 * W, C)
        
        # Handle position embeddings for batched scanlines (patch tokens only)
        pos_rows = None
        if pos is not None and patch_start_idx < total_tokens:
            # Extract position embeddings for patch tokens only
            # pos shape: [B, 2*total_tokens, 2], we need patch part: [B, 2*P, 2]
            pos_left_patch = pos[:, patch_start_idx:patch_start_idx+P, :]   # [B, P, 2]
            pos_right_patch = pos[:, total_tokens+patch_start_idx:, :]      # [B, P, 2]
            pos_patch_combined = torch.cat([pos_left_patch, pos_right_patch], dim=1)  # [B, 2*P, 2]
            
            # Reshape to spatial: [B, H, 2*W, 2] 
            pos_spatial = pos_patch_combined.view(B, H, 2 * W, 2)
            # Reshape for scanline processing: [B*H, 2*W, 2]
            pos_rows = pos_spatial.view(B * H, 2 * W, 2)
        
        # Apply attention to all scanlines in parallel: [B*H, 2*W, C]
        # 🔥 提取注意力权重（如果需要）
        if hasattr(self, '_extract_attention_weights') and self._extract_attention_weights:
            result = self._apply_block_attention(combined_rows, pos_rows, return_attention_weights=True)
            if isinstance(result, tuple):
                updated_combined_rows, scanline_attention_weights = result
                # 保存注意力权重用于后续处理
                self._current_attention_weights = scanline_attention_weights  # [B*H, num_heads, 2*W, 2*W]
            else:
                updated_combined_rows = result
                self._current_attention_weights = None
        else:
            updated_combined_rows = self._apply_block_attention(combined_rows, pos_rows)
            self._current_attention_weights = None
        
        # Reshape back to spatial format: [B, H, 2*W, C]
        updated_combined_spatial = updated_combined_rows.view(B, H, 2 * W, C)
        
        # Split back into left and right views: [B, H, W, C]
        updated_left_spatial = updated_combined_spatial[:, :, :W, :]
        updated_right_spatial = updated_combined_spatial[:, :, W:, :]
        
        # Flatten back to token format: [B, P, C]
        updated_left_patch_tokens = updated_left_spatial.reshape(B, P, C)
        updated_right_patch_tokens = updated_right_spatial.reshape(B, P, C)
        
        # Combine register tokens and patch tokens back
        if left_register_tokens is not None:
            updated_left_tokens = torch.cat([updated_left_register_tokens, updated_left_patch_tokens], dim=1)
            updated_right_tokens = torch.cat([updated_right_register_tokens, updated_right_patch_tokens], dim=1)
        else:
            updated_left_tokens = updated_left_patch_tokens
            updated_right_tokens = updated_right_patch_tokens
        
        return updated_left_tokens, updated_right_tokens
    
    def _apply_block_attention(self, x: torch.Tensor, pos: Optional[torch.Tensor] = None, return_attention_weights: bool = False) -> torch.Tensor:
        """
        Apply standard Block operations (attention + MLP) to input tokens.
        This reuses the parent Block's components.
        
        Args:
            x: Input tokens [N, L, C]
            pos: Position embeddings [N, L, 2] (optional)
            return_attention_weights: Whether to return attention weights
            
        Returns:
            Updated tokens [N, L, C] or (Updated tokens, attention_weights) if return_attention_weights=True
        """
        # 🔥 支持注意力权重提取
        if return_attention_weights:
            # 使用标准Block的forward方法，但要求返回注意力权重
            result = super().forward(x, pos=pos, return_attention_weights=True)
            return result  # (updated_tokens, attention_weights)
        else:
            # Apply attention residual function
            def attn_residual_func(x: torch.Tensor, pos: Optional[torch.Tensor] = None) -> torch.Tensor:
                return self.ls1(self.attn(self.norm1(x), pos=pos))

            def ffn_residual_func(x: torch.Tensor) -> torch.Tensor:
                return self.ls2(self.mlp(self.norm2(x)))

            # Apply attention with residual connection
            x = x + attn_residual_func(x, pos=pos)
            
            # Apply MLP with residual connection
            x = x + ffn_residual_func(x)
            
            return x 