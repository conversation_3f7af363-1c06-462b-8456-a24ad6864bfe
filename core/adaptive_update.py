import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Dict, List, Optional
from core.update import BasicMultiUpdateBlock, BasicMultiUpdateBlock_mix2
from core.adaptive_policy import AdaptiveIterationController

# ---------------------------------------------------------------------------- #
#  1. 自适应基础更新块 - 继承并扩展现有的BasicMultiUpdateBlock
# ---------------------------------------------------------------------------- #

class AdaptiveMultiUpdateBlock(BasicMultiUpdateBlock):
    """
    自适应多尺度更新块，在MonSter原有的更新块基础上集成自适应迭代控制器。
    
    这个类实现了完全向后兼容的设计：
    - 当enable_adaptive=False时，行为与原始BasicMultiUpdateBlock完全相同
    - 当enable_adaptive=True时，启用自适应控制功能
    - 在前面8次迭代(current_iter <= total_iters-8)时，跳过自适应控制，直接使用原始更新
    """
    
    def __init__(self, 
                 args, 
                 hidden_dims: List[int] = [],
                 enable_adaptive: bool = True,
                 gamma: float = 0.1,
                 lambda_eff: float = 0.05):
        """
        Args:
            args: MonSter的配置参数
            hidden_dims: GRU隐藏层维度列表
            enable_adaptive: 是否启用自适应控制
            gamma: 动态预算损失权重
            lambda_eff: 效率损失权重
        """
        # 调用父类初始化
        super().__init__(args, hidden_dims)
        
        self.enable_adaptive = enable_adaptive
        
        # 初始化自适应控制器
        if self.enable_adaptive:
            self.adaptive_controller = AdaptiveIterationController(
                hidden_dims=hidden_dims,
                gamma=gamma,
                lambda_eff=lambda_eff,
                enable_adaptive=True
            )
        else:
            self.adaptive_controller = None
            
        # 记录迭代统计信息
        self.register_buffer('total_iterations', torch.zeros(1))
        self.register_buffer('adaptive_iterations', torch.zeros(1))
    
    def forward(self, 
                net: List[torch.Tensor], 
                inp: List[List[torch.Tensor]], 
                corr: Optional[torch.Tensor] = None, 
                disp: Optional[torch.Tensor] = None, 
                iter04: bool = True, 
                iter08: bool = True, 
                iter16: bool = True, 
                update: bool = True,
                current_iter: Optional[int] = None,
                total_iters: Optional[int] = None) -> Tuple[List[torch.Tensor], torch.Tensor, torch.Tensor, Dict[str, torch.Tensor]]:
        """
        自适应更新块的前向传播 - 仅返回中间结果，不计算损失。
        
        Args:
            net: GRU网络状态列表
            inp: 输入特征列表
            corr: 相关性特征
            disp: 当前视差图
            iter04/iter08/iter16: 各层是否更新
            update: 是否执行更新
            current_iter: 当前迭代次数（用于判断是否应用自适应控制）
            total_iters: 总迭代次数
            
        Returns:
            net: 更新后的网络状态
            mask_feat_4: 掩码特征
            final_delta_disp: 最终视差增量（可能是加权的）
            adaptive_info: 自适应控制的中间结果（权重图、预算等）
        """
        # 1. 执行标准的GRU更新（与原始版本相同）
        net = self._update_gru_states(net, inp, corr, disp, iter04, iter08, iter16)
        
        if not update:
            return net, None, None, {}
        
        # 2. 计算原始视差增量
        delta_disp = self.disp_head(net[0])
        mask_feat_4 = self.mask_feat_4(net[0])
        
        # 3. 🔥 判断是否应该应用自适应控制
        should_apply_adaptive = self.enable_adaptive and self.adaptive_controller is not None
        
        # 如果提供了迭代信息，且在前面8次迭代中，跳过自适应控制
        if (current_iter is not None and total_iters is not None and 
            current_iter <= total_iters - 8):
            should_apply_adaptive = False
        
        if should_apply_adaptive:
            # 应用自适应控制
            policy_features = self.adaptive_controller.policy_feature_extractor(net)
            weight_map = self.adaptive_controller.policy_head_local(policy_features)
            mu_target = self.adaptive_controller.policy_head_global(policy_features)
            
            # 应用权重
            final_delta_disp = weight_map * delta_disp
            
            # 🔥 返回自适应信息
            adaptive_info = {
                'weight_map': weight_map,
                'mu_target': mu_target,
            }
            
            # 更新自适应统计
            self.adaptive_iterations += 1
        else:
            # 🔥 前面8次迭代：使用原始BasicMultiUpdateBlock的行为
            final_delta_disp = delta_disp
            adaptive_info = {}
            
        # 更新总统计信息
        self.total_iterations += 1
        
        return net, mask_feat_4, final_delta_disp, adaptive_info
    
    def _update_gru_states(self, 
                          net: List[torch.Tensor], 
                          inp: List[List[torch.Tensor]], 
                          corr: Optional[torch.Tensor], 
                          disp: Optional[torch.Tensor],
                          iter04: bool, 
                          iter08: bool, 
                          iter16: bool) -> List[torch.Tensor]:
        """
        执行GRU状态更新，保持与原始实现完全一致。
        """
        # 这里复制原始的GRU更新逻辑
        if iter16:
            from core.update import pool2x, interp
            net[2] = self.gru16(net[2], *(inp[2]), pool2x(net[1]))
        if iter08:
            if self.args.n_gru_layers > 2:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]), interp(net[2], net[1]))
            else:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]))
        if iter04:
            motion_features = self.encoder(disp, corr)
            if self.args.n_gru_layers > 1:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features, interp(net[1], net[0]))
            else:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features)
        
        return net
    
    def get_adaptive_statistics(self) -> Dict[str, float]:
        """
        获取自适应控制的统计信息
        """
        total = float(self.total_iterations.item())
        adaptive = float(self.adaptive_iterations.item())
        
        return {
            'total_iterations': total,
            'adaptive_iterations': adaptive,
            'adaptive_ratio': adaptive / max(total, 1.0)
        }

# ---------------------------------------------------------------------------- #
#  2. 自适应混合更新块 - 继承并扩展BasicMultiUpdateBlock_mix2
# ---------------------------------------------------------------------------- #

class AdaptiveMultiUpdateBlock_mix2(BasicMultiUpdateBlock_mix2):
    """
    自适应混合更新块，针对MonSter后期的混合更新阶段。
    支持立体匹配和单目深度的混合更新，同时集成自适应控制。
    在前面8次迭代时跳过自适应控制。
    """
    
    def __init__(self, 
                 args, 
                 hidden_dims: List[int] = [],
                 enable_adaptive: bool = True,
                 gamma: float = 0.1,
                 lambda_eff: float = 0.05):
        super().__init__(args, hidden_dims)
        
        self.enable_adaptive = enable_adaptive
        
        if self.enable_adaptive:
            self.adaptive_controller = AdaptiveIterationController(
                hidden_dims=hidden_dims,
                gamma=gamma,
                lambda_eff=lambda_eff,
                enable_adaptive=True
            )
        else:
            self.adaptive_controller = None
    
    def forward(self, 
                net: List[torch.Tensor], 
                inp: List[List[torch.Tensor]], 
                flaw_stereo: Optional[torch.Tensor] = None, 
                disp: Optional[torch.Tensor] = None, 
                corr: Optional[torch.Tensor] = None,
                flaw_mono: Optional[torch.Tensor] = None, 
                disp_mono: Optional[torch.Tensor] = None, 
                corr_mono: Optional[torch.Tensor] = None,
                iter04: bool = True, 
                iter08: bool = True, 
                iter16: bool = True, 
                update: bool = True,
                current_iter: Optional[int] = None,
                total_iters: Optional[int] = None) -> Tuple[List[torch.Tensor], torch.Tensor, torch.Tensor, Dict[str, torch.Tensor]]:
        """
        自适应混合更新的前向传播 - 仅返回中间结果。
        
        在原有的立体-单目混合更新基础上，增加自适应控制功能。
        在前面8次迭代时跳过自适应控制。
        """
        # 1. 执行标准的混合GRU更新
        net = self._update_gru_states_mix(
            net, inp, flaw_stereo, disp, corr, flaw_mono, disp_mono, corr_mono,
            iter04, iter08, iter16
        )
        
        if not update:
            return net, None, None, {}
        
        # 2. 计算原始视差增量
        delta_disp = self.disp_head(net[0])
        mask_feat_4 = self.mask_feat_4(net[0])
        
        # 3. 🔥 判断是否应该应用自适应控制
        should_apply_adaptive = self.enable_adaptive and self.adaptive_controller is not None
        
        # 如果提供了迭代信息，且在前面8次迭代中，跳过自适应控制
        if (current_iter is not None and total_iters is not None and 
            current_iter <= total_iters - 8):
            should_apply_adaptive = False
        
        if should_apply_adaptive:
            # 应用自适应控制
            policy_features = self.adaptive_controller.policy_feature_extractor(net)
            weight_map = self.adaptive_controller.policy_head_local(policy_features)
            mu_target = self.adaptive_controller.policy_head_global(policy_features)
            
            # 应用权重
            final_delta_disp = weight_map * delta_disp
            
            # 🔥 返回自适应信息
            adaptive_info = {
                'weight_map': weight_map,
                'mu_target': mu_target,
            }
        else:
            # 🔥 前面8次迭代或混合阶段：使用原始BasicMultiUpdateBlock_mix2的行为
            final_delta_disp = delta_disp
            adaptive_info = {}
        
        return net, mask_feat_4, final_delta_disp, adaptive_info
    
    def _update_gru_states_mix(self, 
                              net: List[torch.Tensor], 
                              inp: List[List[torch.Tensor]], 
                              flaw_stereo: Optional[torch.Tensor], 
                              disp: Optional[torch.Tensor], 
                              corr: Optional[torch.Tensor],
                              flaw_mono: Optional[torch.Tensor], 
                              disp_mono: Optional[torch.Tensor], 
                              corr_mono: Optional[torch.Tensor],
                              iter04: bool, 
                              iter08: bool, 
                              iter16: bool) -> List[torch.Tensor]:
        """
        执行混合GRU状态更新。
        """
        from core.update import pool2x, interp
        
        if iter16:
            net[2] = self.gru16(net[2], *(inp[2]), pool2x(net[1]))
        if iter08:
            if self.args.n_gru_layers > 2:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]), interp(net[2], net[1]))
            else:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]))
        if iter04:
            motion_features = self.encoder(disp, corr, flaw_stereo, disp_mono, corr_mono, flaw_mono)
            if self.args.n_gru_layers > 1:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features, interp(net[1], net[0]))
            else:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features)
        
        return net

# ---------------------------------------------------------------------------- #
#  3. 自适应更新块工厂函数
# ---------------------------------------------------------------------------- #

def create_adaptive_update_block(args, 
                                hidden_dims: List[int],
                                update_type: str = 'basic',
                                gamma: float = 0.1,
                                lambda_eff: float = 0.05):
    """
    工厂函数，根据配置创建相应的自适应更新块。
    
    Args:
        args: MonSter配置参数
        hidden_dims: GRU隐藏层维度
        update_type: 更新块类型 ('basic' 或 'mix2')
        gamma: 动态预算损失权重
        lambda_eff: 效率损失权重
    
    Returns:
        相应的自适应更新块实例
    """
    if update_type == 'basic':
        return AdaptiveMultiUpdateBlock(
            args=args,
            hidden_dims=hidden_dims,
            enable_adaptive=True,  # 🔥 始终启用自适应
            gamma=gamma,
            lambda_eff=lambda_eff
        )
    elif update_type == 'mix2':
        return AdaptiveMultiUpdateBlock_mix2(
            args=args,
            hidden_dims=hidden_dims,
            enable_adaptive=True,  # 🔥 始终启用自适应
            gamma=gamma,
            lambda_eff=lambda_eff
        )
    else:
        raise ValueError(f"Unknown update_type: {update_type}")

# ---------------------------------------------------------------------------- #
#  4. 辅助函数：自适应控制器状态管理
# ---------------------------------------------------------------------------- #

def toggle_adaptive_mode(model, enable: bool):
    """
    动态切换模型中所有自适应更新块的启用状态。
    
    Args:
        model: MonSter模型实例
        enable: 是否启用自适应控制
    """
    for module in model.modules():
        if isinstance(module, (AdaptiveMultiUpdateBlock, AdaptiveMultiUpdateBlock_mix2)):
            module.enable_adaptive = enable
            if module.adaptive_controller is not None:
                module.adaptive_controller.enable_adaptive = enable

def get_adaptive_parameters(model):
    """
    获取模型中所有自适应控制器的参数。
    
    Args:
        model: MonSter模型实例
    
    Returns:
        自适应控制器参数的迭代器
    """
    adaptive_params = []
    for module in model.modules():
        if isinstance(module, (AdaptiveMultiUpdateBlock, AdaptiveMultiUpdateBlock_mix2)):
            if module.adaptive_controller is not None:
                adaptive_params.extend(list(module.adaptive_controller.parameters()))
    
    return adaptive_params

def collect_adaptive_statistics(model) -> Dict[str, float]:
    """
    收集模型中所有自适应更新块的统计信息。
    
    Args:
        model: MonSter模型实例
    
    Returns:
        统计信息字典
    """
    stats = {}
    for name, module in model.named_modules():
        if isinstance(module, (AdaptiveMultiUpdateBlock, AdaptiveMultiUpdateBlock_mix2)):
            module_stats = module.get_adaptive_statistics()
            for key, value in module_stats.items():
                stats[f"{name}_{key}"] = value
    
    return stats 