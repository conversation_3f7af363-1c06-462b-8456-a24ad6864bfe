import torch
import torch.nn.functional as F
from typing import Dict, List, Optional
import numpy as np


class AdaptiveLossCalculator:
    """🎯 自适应损失计算器 - 简化稳定版"""
    
    def __init__(self, 
                 lambda_budget: float = 0.05,
                 total_steps: int = 60000):
        """
        Args:
            lambda_budget: 动态预算损失权重
            total_steps: 总训练步数
        """
        self.lambda_budget = lambda_budget
        self.total_steps = total_steps
        
    def compute_sequence_loss(self, 
                            flow_preds: List[torch.Tensor],
                            flow_gt: torch.Tensor,
                            valid: torch.Tensor,
                            adaptive_info_dict: Dict[str, Dict[str, torch.Tensor]],
                            init_disp: Optional[torch.Tensor] = None,
                            gamma: float = 0.8,
                            max_flow: float = 400.0,
                            current_step: Optional[int] = None) -> Dict[str, torch.Tensor]:
        """🎯 计算完整的自适应序列损失 - 简化版"""
        
        # 预处理
        n_predictions = len(flow_preds)
        valid = self._preprocess_valid(valid, flow_gt, max_flow)
        
        # 损失累积器（确保都是tensor类型）
        device = flow_preds[0].device
        total_accuracy_loss = torch.tensor(0.0, device=device)
        total_adaptive_loss = torch.tensor(0.0, device=device)
        loss_details = {'accuracy_loss': 0.0, 'budget_loss': 0.0}
        

        # 1. 初始视差损失
        if init_disp is not None:
            init_loss = self._compute_init_loss(init_disp, flow_gt, valid)
            total_accuracy_loss += init_loss
            loss_details['accuracy_loss'] += init_loss.item()
        
        # 2. 序列损失（精度 + 自适应）
        for i in range(n_predictions):
            iter_weight = self._compute_iter_weight(gamma, n_predictions, i)
            
            # 🔥 尝试多种键名格式来匹配自适应信息
            adaptive_info = None
            possible_keys = [
                f"iter_{i}",                    # 原始格式
                f"iter_{i}_stereo",             # 立体匹配格式
                f"iter_{i}_mono",               # 单目格式
            ]
            
            # 寻找匹配的键
            for key in possible_keys:
                if key in adaptive_info_dict:
                    adaptive_info = adaptive_info_dict[key]
                    break
            
            # 精度损失
            accuracy_loss = self._compute_accuracy_loss(
                flow_preds[i], flow_gt, valid, adaptive_info
            )
            total_accuracy_loss += iter_weight * accuracy_loss
            loss_details['accuracy_loss'] += iter_weight * accuracy_loss.item()
            
            # 自适应损失（仅当有自适应信息时）
            if adaptive_info is not None:
                adaptive_loss = self._compute_adaptive_loss(adaptive_info)
                total_adaptive_loss += iter_weight * adaptive_loss
                
                loss_details['budget_loss'] += iter_weight * adaptive_loss.item()
        
        # 3. EPE指标计算
        epe_metrics = self._compute_epe_metrics(flow_preds[-1], flow_gt, valid)
        
        # 4. 返回结果
        total_loss = total_accuracy_loss + total_adaptive_loss
        
        return {
            'loss': total_loss,
            'accuracy_loss': total_accuracy_loss,
            'adaptive_loss': total_adaptive_loss,
            **loss_details,
            **epe_metrics
        }
    
    def _preprocess_valid(self, valid: torch.Tensor, flow_gt: torch.Tensor, max_flow: float) -> torch.Tensor:
        """预处理有效性掩码 - 简化版"""
        mag = torch.sum(flow_gt**2, dim=1).sqrt()
        
        # 简化维度处理
        if valid.dim() != flow_gt.dim():
            if valid.dim() == 3 and flow_gt.dim() == 4:
                valid = valid.unsqueeze(1)
            elif valid.dim() == 4 and flow_gt.dim() == 3:
                valid = valid.squeeze(1)
        
        # 确保mag和valid维度匹配
        if mag.dim() != valid.dim():
            if mag.dim() == 3 and valid.dim() == 4:
                mag = mag.unsqueeze(1)
        
        valid = (valid >= 0.5) & (mag < max_flow)
        
        # 最终检查
        if valid.shape != flow_gt.shape:
            print(f"⚠️ 维度警告: valid {valid.shape} vs flow_gt {flow_gt.shape}")
            # 强制调整为相同形状
            if flow_gt.dim() == 4:
                valid = valid.view(flow_gt.shape[0], flow_gt.shape[1], flow_gt.shape[2], flow_gt.shape[3])
            else:
                valid = valid.view(flow_gt.shape)
        
        return valid
    
    def _compute_init_loss(self, init_disp: torch.Tensor, flow_gt: torch.Tensor, valid: torch.Tensor) -> torch.Tensor:
        """计算初始视差损失"""
        init_valid = valid.bool() & (~torch.isnan(init_disp)) & (~torch.isinf(init_disp))
        if init_valid.sum() > 0:
            return F.smooth_l1_loss(init_disp[init_valid], flow_gt[init_valid], reduction='mean')
        return torch.tensor(0.0, device=init_disp.device)
    
    def _compute_iter_weight(self, gamma: float, n_predictions: int, iter_idx: int) -> float:
        """计算迭代权重"""
        if n_predictions <= 1:
            return 1.0
        adjusted_gamma = gamma**(15/(n_predictions - 1))
        return adjusted_gamma**(n_predictions - iter_idx - 1)
    
    def _compute_accuracy_loss(self, 
                             pred: torch.Tensor,
                             gt: torch.Tensor,
                             valid: torch.Tensor,
                             adaptive_info: Optional[Dict[str, torch.Tensor]] = None) -> torch.Tensor:
        """计算精度损失（带自适应加权）- 简化版"""
        # 基础L1损失
        i_loss = (pred - gt).abs()
        
        # 检查NaN和Inf
        nan_mask = torch.isnan(i_loss) | torch.isinf(i_loss)
        valid_mask = valid.bool() & (~nan_mask)
        
        # 自适应加权（如果有）- 简化处理
        if adaptive_info is not None and 'weight_map' in adaptive_info:
            weight_map = adaptive_info['weight_map']
            
            # 简化尺寸匹配：只处理最常见的情况
            if weight_map.shape != i_loss.shape:
                try:
                    # 如果是不同分辨率，进行插值
                    if weight_map.dim() == 3:  # [B, H, W]
                        weight_map = weight_map.unsqueeze(1)  # [B, 1, H, W]
                    
                    weight_map = F.interpolate(weight_map, size=i_loss.shape[-2:], 
                                             mode='bilinear', align_corners=False)
                    
                    # 调整输出维度
                    if i_loss.dim() == 3 and weight_map.dim() == 4:
                        weight_map = weight_map.squeeze(1)
                except Exception as e:
                    print(f"⚠️ 权重图插值失败: {e}, 使用标准损失")
                    weight_map = None
            
            if weight_map is not None:
                # 防止权重图中的异常值
                weight_map = torch.clamp(weight_map, 0.0, 1.0)
                
                # 组合权重
                combined_weight = valid_mask.float() * weight_map
                
                if combined_weight.sum() > 0:
                    return (i_loss * combined_weight).sum() / (combined_weight.sum() + 1e-8)
        
        # 标准损失计算
        if valid_mask.sum() > 0:
            return i_loss[valid_mask].mean()
        return torch.tensor(0.0, device=pred.device)
    
    def _compute_adaptive_loss(self, adaptive_info: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算自适应损失（仅预算损失）- 简化版"""
        # 只保留动态预算损失
        budget_loss = self._compute_budget_dynamic_loss(adaptive_info)
        
        return self.lambda_budget * budget_loss
    

    def _compute_budget_dynamic_loss(self, adaptive_info: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算动态预算损失 - 简化版"""
        if 'mu_target' not in adaptive_info or 'weight_map' not in adaptive_info:
            return torch.tensor(0.0)
        
        mu_target = adaptive_info['mu_target']  # [B, 1]
        weight_map = adaptive_info['weight_map']  # [B, H, W] 或 [B, 1, H, W]
        
        # 检查异常值
        if torch.isnan(weight_map).any() or torch.isinf(weight_map).any():
            print("⚠️ weight_map包含NaN或Inf，返回0预算损失")
            return torch.tensor(0.0, device=weight_map.device)
            
        if torch.isnan(mu_target).any() or torch.isinf(mu_target).any():
            print("⚠️ mu_target包含NaN或Inf，返回0预算损失")
            return torch.tensor(0.0, device=mu_target.device)
        
        # 计算实际预算
        try:
            if weight_map.dim() == 3:  # [B, H, W]
                actual_budget = weight_map.mean(dim=[1, 2])  # [B]
            else:  # [B, 1, H, W]
                actual_budget = weight_map.mean(dim=[2, 3])  # [B, 1]
            
            # 简化维度匹配
            if actual_budget.shape != mu_target.shape:
                if mu_target.dim() == 1 and actual_budget.dim() == 2:
                    mu_target = mu_target.unsqueeze(1)
                elif mu_target.dim() == 2 and actual_budget.dim() == 1:
                    actual_budget = actual_budget.unsqueeze(1)
            
            # 核心损失：|mean(w) - μ_target|²
            budget_diff = actual_budget - mu_target.detach()
            return torch.clamp(budget_diff**2, 0.0, 10.0).mean()  # 限制损失范围
            
        except Exception as e:
            print(f"⚠️ 预算损失计算失败: {e}")
            return torch.tensor(0.0, device=weight_map.device)
    
    def _compute_epe_metrics(self, pred: torch.Tensor, gt: torch.Tensor, valid: torch.Tensor) -> Dict[str, torch.Tensor]:
        """计算EPE指标"""
        epe = torch.sum((pred - gt)**2, dim=1).sqrt()
        
        # 处理异常值
        nan_mask = torch.isnan(epe) | torch.isinf(epe)
        valid_epe_mask = valid.view(-1) & (~nan_mask.view(-1))
        
        if valid_epe_mask.sum() == 0:
            # 没有有效像素
            device = gt.device
            return {
                'train/epe': torch.tensor(0.0, device=device),
                'train/1px': torch.tensor(0.0, device=device),
                'train/3px': torch.tensor(0.0, device=device),
                'train/5px': torch.tensor(0.0, device=device),
            }
        
        epe_valid = epe.view(-1)[valid_epe_mask]
        
        return {
            'train/epe': epe_valid.mean(),
            'train/1px': (epe_valid < 1).float().mean(),
            'train/3px': (epe_valid < 3).float().mean(),
            'train/5px': (epe_valid < 5).float().mean(),
        }


def create_adaptive_loss_calculator(config: dict) -> AdaptiveLossCalculator:
    """🏭 工厂函数：创建自适应损失计算器 - 简化版"""
    
    return AdaptiveLossCalculator(
        lambda_budget=config.get('adaptive_lambda_budget', 0.05),
        total_steps=config.get('total_step', 60000)
    )

