import torch
import torch.nn as nn
import torch.nn.functional as F
from xformers.ops import memory_efficient_attention
import time  # 仅用于测试代码


# 2D旋转位置编码（RoPE）实现 - 简化版本
class RotaryPositionEmbedding2D(nn.Module):
    """
    2D旋转位置编码，专门针对视差上采样任务优化
    简化版本：q和k使用相同的位置编码
    """
    def __init__(self, dim, max_position=10000, learnable_freq=False):
        super().__init__()
        self.dim = dim
        self.max_position = max_position
        
        # 计算频率，分别处理x和y维度
        half_dim = dim // 4  # 每个维度用dim//4
        
        if learnable_freq:
            # 可学习的频率参数
            self.inv_freq_x = nn.Parameter(torch.randn(half_dim) * 0.02)
            self.inv_freq_y = nn.Parameter(torch.randn(half_dim) * 0.02)
        else:
            # 固定频率
            inv_freq = 1.0 / (max_position ** (torch.arange(0, half_dim, dtype=torch.float) / half_dim))
            self.register_buffer('inv_freq_x', inv_freq)
            self.register_buffer('inv_freq_y', inv_freq)
    
    def forward(self, q, k, positions):
        """
        简化接口：q和k使用相同的位置编码
        
        Args:
            q: (batch_size, seq_len, num_heads, head_dim) - 查询特征
            k: (batch_size, seq_len, num_heads, head_dim) - 键特征  
            positions: (seq_len, 2) - 位置坐标 [x, y]
        Returns:
            q_rot, k_rot: 应用RoPE后的查询和键特征
        """
        # 对q和k应用相同的位置编码
        q_rot = self.apply_rope_2d(q, positions)
        k_rot = self.apply_rope_2d(k, positions)
        
        return q_rot, k_rot
    
    def apply_rope_2d(self, x, positions):
        """
        对2D位置应用旋转位置编码
        
        Args:
            x: (batch_size, seq_len, num_heads, head_dim)
            positions: (seq_len, 2) - [x, y] 位置坐标
        Returns:
            x_rot: 应用RoPE后的特征
        """
        batch_size, seq_len, num_heads, head_dim = x.shape
        
        # 确保positions和频率与输入张量x具有相同的dtype和设备
        device = x.device
        dtype = x.dtype
        
        positions = positions.to(device=device, dtype=dtype)
        inv_freq_x = self.inv_freq_x.to(device=device, dtype=dtype)
        inv_freq_y = self.inv_freq_y.to(device=device, dtype=dtype)
        
        # 分离x和y坐标
        pos_x = positions[:, 0]  # (seq_len,)
        pos_y = positions[:, 1]  # (seq_len,)
        
        # 计算频率乘积
        freqs_x = torch.outer(pos_x, inv_freq_x)  # (seq_len, half_dim)
        freqs_y = torch.outer(pos_y, inv_freq_y)  # (seq_len, half_dim)
        
        # 生成sin和cos
        sin_x, cos_x = freqs_x.sin(), freqs_x.cos()
        sin_y, cos_y = freqs_y.sin(), freqs_y.cos()
        
        # 将特征分成4部分：[x_real, x_imag, y_real, y_imag]
        quarter_dim = head_dim // 4
        x_real, x_imag = x[..., :quarter_dim], x[..., quarter_dim:2*quarter_dim]
        y_real, y_imag = x[..., 2*quarter_dim:3*quarter_dim], x[..., 3*quarter_dim:]
        
        # 应用旋转变换到x维度
        x_real_rot = x_real * cos_x.unsqueeze(0).unsqueeze(2) - x_imag * sin_x.unsqueeze(0).unsqueeze(2)
        x_imag_rot = x_real * sin_x.unsqueeze(0).unsqueeze(2) + x_imag * cos_x.unsqueeze(0).unsqueeze(2)
        
        # 应用旋转变换到y维度
        y_real_rot = y_real * cos_y.unsqueeze(0).unsqueeze(2) - y_imag * sin_y.unsqueeze(0).unsqueeze(2)
        y_imag_rot = y_real * sin_y.unsqueeze(0).unsqueeze(2) + y_imag * cos_y.unsqueeze(0).unsqueeze(2)
        
        # 重新组合
        x_rot = torch.cat([x_real_rot, x_imag_rot, y_real_rot, y_imag_rot], dim=-1)
        
        return x_rot


# RGB特征编码器 - 将RGB图像编码为索引向量
class RGBEncoder(nn.Module):
    """将原尺寸RGB图像编码为高维特征，然后生成索引向量"""
    def __init__(self, hidden_dim=64):
        super().__init__()
        
        # RGB特征提取器
        self.rgb_encoder = nn.Sequential(
            nn.Conv2d(3, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, hidden_dim, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, rgb):
        """
        Args:
            rgb: (B, 3, H, W) - 原尺寸RGB图像
        Returns:
            queries: (B, hidden_dim, H, W) - 索引向量
        """
        queries = self.rgb_encoder(rgb)
        return queries


# 视差特征融合器 - 融合视差和1/4特征
class DisparityFeatureFusion(nn.Module):
    """将视差图编码并与1/4特征融合，生成keys_values"""
    def __init__(self, feat_channels=32, hidden_dim=64):
        super().__init__()
        
        # 视差编码器
        self.disp_encoder = nn.Sequential(
            nn.Conv2d(1, 16, kernel_size=3, padding=1),
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True)
        )
        
        # 特征融合器 (视差特征 + 1/4特征)
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(32 + feat_channels, hidden_dim, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim, hidden_dim, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, disp, feat_1_4):
        """
        Args:
            disp: (B, 1, H/4, W/4) - 1/4尺寸视差图
            feat_1_4: (B, feat_channels, H/4, W/4) - 1/4尺寸特征图
        Returns:
            keys_values: (B, hidden_dim, H/4, W/4) - 融合后的键值特征
        """
        # 编码视差
        disp_feats = self.disp_encoder(disp)  # (B, 32, H/4, W/4)
        
        # 拼接视差特征和1/4特征
        concat_feats = torch.cat([disp_feats, feat_1_4], dim=1)  # (B, 32+feat_channels, H/4, W/4)
        
        # 融合特征
        keys_values = self.feature_fusion(concat_feats)  # (B, hidden_dim, H/4, W/4)
        
        return keys_values

# 优化的注意力权重生成器（增加RoPE支持）
class OptimizedAttentionWeightGenerator(nn.Module):
    """基于RGB索引向量和视差特征融合的注意力权重生成器，增加RoPE位置编码"""
    def __init__(self, feat_channels=32, hidden_dim=64, num_heads=4, window_size=32, use_rope=True):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        self.window_size = window_size
        self.use_rope = use_rope
        
        # RGB编码器 - 生成索引向量(queries)
        self.rgb_encoder = RGBEncoder(hidden_dim=hidden_dim)
        
        # 视差特征融合器 - 生成keys_values
        self.disp_feat_fusion = DisparityFeatureFusion(feat_channels=feat_channels, hidden_dim=hidden_dim)
        
        # 注意力投影层
        self.q_proj = nn.Linear(hidden_dim, hidden_dim, bias=True)
        self.k_proj = nn.Linear(hidden_dim, hidden_dim, bias=True)
        self.v_proj = nn.Linear(hidden_dim, hidden_dim, bias=True)
        
        # 输出投影层
        self.out_proj = nn.Linear(hidden_dim, hidden_dim, bias=True)
        
        # RoPE位置编码（如果启用）
        if self.use_rope:
            self.rope_2d = RotaryPositionEmbedding2D(
                dim=self.head_dim, 
                max_position=max(window_size * 4, 1000),  # 考虑多尺度特点
                learnable_freq=True  # 使用可学习频率适应视差上采样任务
            )
            
            # 预计算窗口位置坐标，避免重复计算
            y_coords, x_coords = torch.meshgrid(
                torch.arange(window_size),
                torch.arange(window_size),
                indexing='ij'
            )
            positions = torch.stack([x_coords.flatten(), y_coords.flatten()], dim=1).float()
            self.register_buffer('window_positions', positions)  # (window_size*window_size, 2)
        
        # 最终输出层：直接输出9个通道，用于softmax
        self.output_head = nn.Sequential(
            nn.Conv2d(hidden_dim, hidden_dim // 2, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim // 2, 9, kernel_size=1)  # 9个通道对应3x3邻域
        )

    def forward(self, rgb, disp, feat_1_4):
        """
        Args:
            rgb: (B, 3, H, W) - 原尺寸RGB图像
            disp: (B, 1, H/4, W/4) - 1/4尺寸视差图
            feat_1_4: (B, feat_channels, H/4, W/4) - 1/4尺寸特征图
        Returns:
            weights: (B, 9, H, W) - 上采样权重，已经过softmax
        """
        b, _, h, w = rgb.shape
        
        # Step 1: RGB编码生成索引向量(queries)
        queries = self.rgb_encoder(rgb)  # (B, hidden_dim, H, W)
        
        # Step 2: 视差特征融合生成keys_values
        keys_values = self.disp_feat_fusion(disp, feat_1_4)  # (B, hidden_dim, H/4, W/4)
        
        # Step 3: 窗口注意力融合
        attended_features = self.window_cross_attention(queries, keys_values)
        
        # Step 4: 生成9个通道的输出，在dim=1进行softmax
        raw_output = self.output_head(attended_features)  # (B, 9, H, W)
        weights = F.softmax(raw_output, dim=1)  # 确保权重和为1
        
        return weights

    def window_cross_attention(self, queries, keys_values):
        """
        在局部窗口内进行交叉注意力计算，增加RoPE位置编码支持
        
        Args:
            queries: (B, hidden_dim, H, W) - 高分辨率查询特征
            keys_values: (B, hidden_dim, H/4, W/4) - 低分辨率键值特征
            
        Returns:
            attended_features: (B, hidden_dim, H, W) - 注意力融合后的特征
        """
        b, c, h, w = queries.shape
        window_size = self.window_size
        
        # 计算窗口数量
        num_windows_h = (h + window_size - 1) // window_size
        num_windows_w = (w + window_size - 1) // window_size
        
        # 对查询特征进行padding以适应窗口划分
        pad_h = num_windows_h * window_size - h
        pad_w = num_windows_w * window_size - w
        
        if pad_h > 0 or pad_w > 0:
            queries_padded = F.pad(queries, (0, pad_w, 0, pad_h))
        else:
            queries_padded = queries
            
        # 重塑为窗口格式: (B, C, num_windows_h, window_size, num_windows_w, window_size)
        queries_window = queries_padded.view(b, c, num_windows_h, window_size, num_windows_w, window_size)
        queries_window = queries_window.permute(0, 2, 4, 1, 3, 5)  # (B, num_windows_h, num_windows_w, C, window_size, window_size)
        queries_window = queries_window.contiguous().view(b * num_windows_h * num_windows_w, c, window_size * window_size)
        queries_flat = queries_window.transpose(1, 2)  # (B*num_windows, window_size*window_size, C)
        
        # 对键值特征进行插值以匹配查询特征的窗口大小
        keys_values_up = F.interpolate(keys_values, size=(h, w), mode='bilinear', align_corners=False)
        
        if pad_h > 0 or pad_w > 0:
            keys_values_up_padded = F.pad(keys_values_up, (0, pad_w, 0, pad_h))
        else:
            keys_values_up_padded = keys_values_up
            
        # 重塑为窗口格式
        kv_window = keys_values_up_padded.view(b, c, num_windows_h, window_size, num_windows_w, window_size)
        kv_window = kv_window.permute(0, 2, 4, 1, 3, 5)  # (B, num_windows_h, num_windows_w, C, window_size, window_size)
        kv_window = kv_window.contiguous().view(b * num_windows_h * num_windows_w, c, window_size * window_size)
        kv_flat = kv_window.transpose(1, 2)  # (B*num_windows, window_size*window_size, C)
        
        # 投影查询、键、值
        q = self.q_proj(queries_flat)  # (B*num_windows, window_size*window_size, hidden_dim)
        k = self.k_proj(kv_flat)       # (B*num_windows, window_size*window_size, hidden_dim)
        v = self.v_proj(kv_flat)       # (B*num_windows, window_size*window_size, hidden_dim)
        
        # 重塑为多头格式
        q = q.view(-1, window_size*window_size, self.num_heads, self.head_dim)  # (B*num_windows, window_size*window_size, num_heads, head_dim)
        k = k.view(-1, window_size*window_size, self.num_heads, self.head_dim)  # (B*num_windows, window_size*window_size, num_heads, head_dim)
        v = v.view(-1, window_size*window_size, self.num_heads, self.head_dim)  # (B*num_windows, window_size*window_size, num_heads, head_dim)
        
        # 应用RoPE位置编码（如果启用）
        if self.use_rope:
            # 使用预计算的窗口位置坐标，避免重复计算
            # 由于keys_values已经上采样到与queries相同尺寸，使用相同的位置编码
            positions = self.window_positions  # (window_size*window_size, 2)
            
            # 确保位置编码与q、k的dtype一致
            positions = positions.to(q.dtype)
            
            # 应用RoPE到查询和键，使用相同的位置编码
            q, k = self.rope_2d(q, k, positions)
        
        # 使用内存高效的注意力计算
        attended_queries = memory_efficient_attention(q, k, v)
        attended_queries = attended_queries.reshape(-1, window_size*window_size, self.hidden_dim)  # (B*num_windows, window_size*window_size, hidden_dim)
        
        # 输出投影
        attended_queries = self.out_proj(attended_queries)  # (B*num_windows, window_size*window_size, hidden_dim)
        
        # 重塑回空间维度
        attended_window = attended_queries.view(b, num_windows_h, num_windows_w, window_size, window_size, c)
        attended_window = attended_window.permute(0, 5, 1, 3, 2, 4).contiguous()
        attended_features = attended_window.view(b, c, num_windows_h * window_size, num_windows_w * window_size)
        
        # 移除padding
        if pad_h > 0 or pad_w > 0:
            attended_features = attended_features[:, :, :h, :w]
            
        return attended_features

# 优化的视差上采样器
class DisparityUpsampler(nn.Module):
    """优化的基于注意力机制的视差上采样器，支持RoPE位置编码"""
    def __init__(self, use_rope=True):
        super().__init__()
        
        self.attention_weight_gen = OptimizedAttentionWeightGenerator(
            feat_channels=32, 
            hidden_dim=16,    # 隐藏层维度
            num_heads=2,      # 注意力头数
            window_size=16,   # 窗口大小
            use_rope=use_rope # RoPE开关
        )
        
    def context_upsample(self, disp_low, up_weights):
        """保持原有的context_upsample逻辑"""
        b, c, h, w = disp_low.shape
        
        disp_unfold = F.unfold(disp_low.reshape(b,c,h,w), 3, 1, 1).reshape(b,-1,h,w)
        disp_unfold = F.interpolate(disp_unfold, (h*4, w*4), mode='nearest').reshape(b,9,h*4,w*4)
        
        disp = (disp_unfold * up_weights).sum(1)
        
        return disp
        
    def forward(self, disp, mask_feat_4, rgb):
        """
        优化的视差上采样方法
        
        Args:
            disp: (B, 1, H/4, W/4) - 低分辨率视差
            mask_feat_4: (B, 32, H/4, W/4) - 1/4尺度mask特征
            rgb: (B, 3, H, W) - 高分辨率RGB图像
        Returns:
            up_disp: (B, 1, H, W) - 上采样后的视差
        """  
        # Step 1: 用优化的注意力机制生成智能权重
        # 注意：这里传入的是视差和mask特征，而不是xspx特征
        spx_pred = self.attention_weight_gen(rgb, disp, mask_feat_4)  # (B, 9, H, W)
        
        # Step 2: 保持原有的视差缩放和上采样逻辑
        up_disp = self.context_upsample(disp * 4., spx_pred).unsqueeze(1)
        
        return up_disp

# 使用示例和测试
if __name__ == "__main__":
    # 创建测试数据
    b, h, w = 1, 512, 960   # 减小测试尺寸
    
    # 输入数据
    disp = torch.rand(b, 1, h//4, w//4) * 16  # 视差范围0-16 (在1/4尺度)
    mask_feat_4 = torch.rand(b, 32, h//4, w//4)  # 1/4尺度特征
    rgb = torch.rand(b, 3, h, w)                 # 高分辨率RGB
    
    # 创建优化的上采样器
    upsampler = DisparityUpsampler(use_rope=True).cuda()
    
    print("测试优化的上采样器（启用RoPE）...")
    start = time.time()
    for i in range(10):  # 减少测试次数
        up_disp = upsampler(disp.cuda(), mask_feat_4.cuda(), rgb.cuda())
    print(f"优化上采样耗时: {(time.time() - start) * 100:.2f} ms")
    
    print(f"输入视差形状: {disp.shape}, 范围: [{disp.min():.2f}, {disp.max():.2f}]")
    print(f"优化输出视差形状: {up_disp.shape}, 范围: [{up_disp.min():.2f}, {up_disp.max():.2f}]")
    print(f"优化缩放系数: {(up_disp.max() / disp.max()):.2f} (应该接近4.0)")
    
    # 检查参数量
    optimized_params = sum(p.numel() for p in upsampler.parameters())
    print(f"优化模型总参数量: {optimized_params:,}")
    
    # 测试各个组件
    rgb_encoder = upsampler.attention_weight_gen.rgb_encoder
    disp_fusion = upsampler.attention_weight_gen.disp_feat_fusion
    
    # 测试RGB编码器
    queries = rgb_encoder(rgb.cuda())
    print(f"RGB编码器输出: {queries.shape}")
    
    # 测试视差特征融合器
    keys_values = disp_fusion(disp.cuda(), mask_feat_4.cuda())
    print(f"视差特征融合器输出: {keys_values.shape}")
    
    # 测试不同RoPE设置
    print("\n" + "=" * 50)
    print("测试RoPE开关效果:")
    
    # 测试启用RoPE
    upsampler_with_rope = DisparityUpsampler(use_rope=True).cuda()
    start = time.time()
    up_disp_rope = upsampler_with_rope(disp.cuda(), mask_feat_4.cuda(), rgb.cuda())
    rope_time = time.time() - start
    
    # 测试禁用RoPE
    upsampler_no_rope = DisparityUpsampler(use_rope=False).cuda()
    start = time.time()
    up_disp_no_rope = upsampler_no_rope(disp.cuda(), mask_feat_4.cuda(), rgb.cuda())
    no_rope_time = time.time() - start
    
    print(f"启用RoPE耗时: {rope_time * 1000:.2f} ms")
    print(f"禁用RoPE耗时: {no_rope_time * 1000:.2f} ms")
    print(f"RoPE开销: {((rope_time - no_rope_time) / no_rope_time * 100):.1f}%")
    
    print("=" * 50)
    print("优化的上采样器架构总结:")
    print("1. RGB编码器: 直接从RGB生成索引向量(queries)")
    print("2. 视差特征融合器: 视差编码 + 特征融合 → keys_values")
    print("3. 窗口注意力: 局部交叉注意力计算")
    print("4. RoPE位置编码: 统一位置编码(queries和keys使用相同坐标)")
    print("5. 输出处理: softmax(dim=1) + context_upsample")
    print("6. 优化特性: 预计算位置坐标, 简化RoPE接口")
    print("=" * 50)
