from setuptools import setup, Extension
from pybind11.setup_helpers import Pybind11Extension, build_ext
from pybind11 import get_cmake_dir
import pybind11
from torch.utils.cpp_extension import BuildExtension, CUDAExtension
import torch
import os

# 🔥 从PyTorch获取CUDA主目录
cuda_home = os.environ.get('CUDA_HOME')
if not cuda_home:
    try:
        import torch
        cuda_home = os.path.join(os.path.dirname(torch.utils.cpp_extension.CUDA_HOME), '..')
    except (ImportError, AttributeError):
        pass

print(f"🔧 使用CUDA主目录: {cuda_home}")

# CUDA扩展定义
ext_modules = [
    CUDAExtension(
        name='cuda_geo_sampling',
        sources=[
            'cuda_geo_sampling.cu',
        ],
        include_dirs=[
            # 添加PyTorch和CUDA的头文件路径
            os.path.join(cuda_home, 'include') if cuda_home else '',
        ],
        library_dirs=[
            os.path.join(cuda_home, 'lib64') if cuda_home else '',
        ],
        extra_compile_args={
            'cxx': ['-O3'],
            'nvcc': [
                '-O3',
                '--use_fast_math',
                '-gencode=arch=compute_70,code=sm_70',
                '-gencode=arch=compute_75,code=sm_75', 
                '-gencode=arch=compute_80,code=sm_80',
                '-gencode=arch=compute_86,code=sm_86',
            ]
        },
    ),
]

if __name__ == '__main__':
    setup(
        name='cuda_geo_sampling',
        ext_modules=ext_modules,
        cmdclass={
            'build_ext': BuildExtension
        },
        zip_safe=False,
    ) 