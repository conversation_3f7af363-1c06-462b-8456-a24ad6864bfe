#include <torch/extension.h>
#include <torch/csrc/api/include/torch/nn/functional/vision.h> // For grid_sample
#include <vector>

namespace F = torch::nn::functional;

// This version bypasses the custom CUDA kernel and directly calls the
// underlying PyTorch C++ API for grid_sample. This guarantees
// perfect numerical consistency with the Python version.
std::vector<torch::Tensor> fused_geo_sampling_cuda(
    torch::Tensor geo_volume,    // [B*H*W, C, 1, D]
    torch::Tensor init_corr,     // [B*H*W, 1, 1, W2]
    torch::Tensor disp,          // [B, 1, H, W]
    torch::Tensor coords,        // [B, H, W, 1]
    int radius,
    float scale_factor) {

    const auto b = disp.size(0);
    const auto h = disp.size(2);
    const auto w = disp.size(3);

    // Replicate the PyTorch forward logic directly in C++
    torch::Tensor dx = torch::linspace(-radius, radius, 2 * radius + 1, disp.options());
    dx = dx.view({1, 1, 2 * radius + 1, 1});

    // --- Geometric Sampling ---
    torch::Tensor x0_geo = dx + disp.reshape({b * h * w, 1, 1, 1}) / scale_factor;
    torch::Tensor y0 = torch::zeros_like(x0_geo);
    torch::Tensor disp_lvl = torch::cat({x0_geo, y0}, -1);
    
    // Create F::GridSampleFuncOptions to match bilinear_sampler
    auto grid_sample_options = F::GridSampleFuncOptions()
                                .mode(torch::kBilinear)
                                .padding_mode(torch::kZeros)
                                .align_corners(true);
    
    // Normalize coordinates for grid_sample
    const auto D = geo_volume.size(3);
    disp_lvl.select(-1, 0) = 2.0f * disp_lvl.select(-1, 0) / (D - 1) - 1.0f;

    torch::Tensor geo_sampled = F::grid_sample(geo_volume, disp_lvl, grid_sample_options);
    torch::Tensor geo_output = geo_sampled.view({b, h, w, -1});

    // --- Correlation Sampling ---
    const auto W2 = init_corr.size(3);
    torch::Tensor coords_reshaped = coords.reshape({b * h * w, 1, 1, 1});
    torch::Tensor x0_corr = coords_reshaped / scale_factor - disp.reshape({b * h * w, 1, 1, 1}) / scale_factor + dx;
    torch::Tensor init_coords_lvl = torch::cat({x0_corr, y0}, -1);

    // Normalize coordinates for grid_sample
    init_coords_lvl.select(-1, 0) = 2.0f * init_coords_lvl.select(-1, 0) / (W2 - 1) - 1.0f;
    
    torch::Tensor corr_sampled = F::grid_sample(init_corr, init_coords_lvl, grid_sample_options);
    torch::Tensor corr_output = corr_sampled.view({b, h, w, -1});

    return {geo_output, corr_output};
}

// Since we are no longer using a custom kernel,
// we can also use the C++ API for correlation for consistency.
torch::Tensor optimized_correlation_cuda(torch::Tensor fmap1, torch::Tensor fmap2) {
    auto B = fmap1.size(0);
    auto D = fmap1.size(1);
    auto H = fmap1.size(2);
    auto W1 = fmap1.size(3);
    auto W2 = fmap2.size(3);
    
    fmap1 = fmap1.view({B, D, H, W1});
    fmap2 = fmap2.view({B, D, H, W2});
    
    torch::Tensor corr = torch::einsum("aijk,aijh->ajkh", {fmap1, fmap2});
    corr = corr.reshape({B, H, W1, 1, W2}).contiguous();
    return corr;
}

// Python binding
PYBIND11_MODULE(TORCH_EXTENSION_NAME, m) {
    m.def("fused_geo_sampling", &fused_geo_sampling_cuda, "Fused geometric sampling (CUDA C++ API Wrapper)");
    m.def("optimized_correlation", &optimized_correlation_cuda, "Optimized correlation (CUDA C++ API Wrapper)");
} 