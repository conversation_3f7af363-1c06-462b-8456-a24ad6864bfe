"""
一个纯粹的、无备用方案的CUDA几何编码实现。
如果CUDA扩展不可用，导入此文件将直接失败。
"""

import torch
import torch.nn.functional as F
from torch.autograd import Function
from utils.utils import bilinear_sampler
import cuda_geo_sampling as cuda_ge_pling


class FusedGeoSamplingFunction(Function):
    """
    支持梯度传播的CUDA几何采样autograd函数。
    此版本只包含CUDA路径。
    """
    
    @staticmethod
    def forward(ctx, geo_volume, init_corr, disp, coords, radius, scale_factor):
        """
        前向传播 (仅限CUDA)
        """
        geo_output, corr_output = cuda_ge_pling.fused_geo_sampling(
            geo_volume, init_corr, disp, coords, radius, scale_factor
        )
        
        # 📝 保存反向传播需要的信息
        # 注意：当前的反向传播是基于PyTorch实现的，因为它更简单且不易出错。
        # 对于仅前向推理的场景，这已经足够快。
        ctx.save_for_backward(geo_volume, init_corr, disp, coords)
        ctx.radius = radius
        ctx.scale_factor = scale_factor
        
        return geo_output, corr_output
    
    @staticmethod
    def backward(ctx, grad_geo_output, grad_corr_output):
        """
        反向传播 - 依然使用PyTorch的autograd进行计算，以确保梯度的正确性。
        """
        geo_volume, init_corr, disp, coords = ctx.saved_tensors
        radius = ctx.radius
        scale_factor = ctx.scale_factor

        # 为了计算梯度，我们需要重新在PyTorch中执行前向传播
        with torch.enable_grad():
            geo_volume_grad = geo_volume.detach().requires_grad_(True)
            init_corr_grad = init_corr.detach().requires_grad_(True)
            disp_grad = disp.detach().requires_grad_(True)
            coords_grad = coords.detach().requires_grad_(True)

            # 使用PyTorch的bilinear_sampler进行前向计算以获取梯度
            b, _, h, w = disp.shape
            dx = torch.linspace(-radius, radius, 2*radius+1, device=disp.device).view(1, 1, 2*radius+1, 1)
            x0_geo = dx + disp.reshape(b*h*w, 1, 1, 1) / scale_factor
            y0 = torch.zeros_like(x0_geo)
            disp_lvl = torch.cat([x0_geo, y0], dim=-1)
            geo_sampled = bilinear_sampler(geo_volume_grad, disp_lvl)
            
            coords_reshaped = coords.reshape(b*h*w, 1, 1, 1)
            x0_corr = coords_reshaped/scale_factor - disp.reshape(b*h*w, 1, 1, 1)/scale_factor + dx
            init_coords_lvl = torch.cat([x0_corr, y0], dim=-1)
            corr_sampled = bilinear_sampler(init_corr_grad, init_coords_lvl)

            # 计算总损失
            total_loss = torch.sum(geo_sampled * grad_geo_output.view_as(geo_sampled)) + \
                         torch.sum(corr_sampled * grad_corr_output.view_as(corr_sampled))
            
            # 计算梯度
            grads = torch.autograd.grad(
                total_loss,
                [geo_volume_grad, init_corr_grad, disp_grad, coords_grad]
            )
            
            return grads[0], grads[1], grads[2], grads[3], None, None


class CUDAAcceleratedGeoEncodingFixed:
    """
    CUDA加速的几何编码 (纯净版)
    """
    
    def __init__(self, init_fmap1, init_fmap2, geo_volume, num_levels=2, radius=4):
        self.num_levels = num_levels
        self.radius = radius
        self._build_pyramids(init_fmap1, init_fmap2, geo_volume)
    
    def _build_pyramids(self, init_fmap1, init_fmap2, geo_volume):
        init_corr = cuda_ge_pling.optimized_correlation(init_fmap1, init_fmap2)
        
        b, h, w, _, w2 = init_corr.shape
        b, c, d, h, w = geo_volume.shape
        geo_volume = geo_volume.permute(0, 3, 4, 1, 2).reshape(b*h*w, c, 1, d)
        init_corr = init_corr.reshape(b*h*w, 1, 1, w2)
        
        self.geo_volume_pyramid = [geo_volume]
        self.init_corr_pyramid = [init_corr]
        
        for i in range(self.num_levels-1):
            geo_volume = F.avg_pool2d(geo_volume, [1,2], stride=[1,2])
            self.geo_volume_pyramid.append(geo_volume)
        
        for i in range(self.num_levels-1):
            init_corr = F.avg_pool2d(init_corr, [1,2], stride=[1,2])
            self.init_corr_pyramid.append(init_corr)
    
    def __call__(self, disp, coords, level=1):
        b, _, h, w = disp.shape
        out_pyramid = []
        
        coords_reshaped = coords.reshape(b, h, w, 1)
        
        for i in range(level):
            geo_volume = self.geo_volume_pyramid[i]
            init_corr = self.init_corr_pyramid[i]
            scale_factor = 2.0 ** i
            
            geo_output, corr_output = FusedGeoSamplingFunction.apply(
                geo_volume, init_corr, disp, coords_reshaped,
                self.radius, scale_factor
            )
            
            geo_features = geo_output.view(b, h, w, -1)
            corr_features = corr_output.view(b, h, w, -1)
            
            out_pyramid.append(geo_features)
            out_pyramid.append(corr_features)
        
        out = torch.cat(out_pyramid, dim=-1)
        return out.permute(0, 3, 1, 2).contiguous().float()


def create_gradient_safe_geo_encoding(*args, **kwargs):
    # 这个工厂函数现在只返回CUDA版本。如果导入失败，则在顶层就会崩溃。
    return CUDAAcceleratedGeoEncodingFixed(*args, **kwargs) 