import torch
import torch.nn.functional as F
import numpy as np
from scipy import interpolate


class InputPadder:
    """ Pads images such that dimensions are divisible by 8 """
    def __init__(self, dims, mode='sintel', divis_by=8):
        self.ht, self.wd = dims[-2:]
        pad_ht = (((self.ht // divis_by) + 1) * divis_by - self.ht) % divis_by
        pad_wd = (((self.wd // divis_by) + 1) * divis_by - self.wd) % divis_by
        if mode == 'sintel':
            self._pad = [pad_wd//2, pad_wd - pad_wd//2, pad_ht//2, pad_ht - pad_ht//2]
        else:
            self._pad = [pad_wd//2, pad_wd - pad_wd//2, 0, pad_ht]

    def pad(self, *inputs):
        assert all((x.ndim == 4) for x in inputs)
        return [F.pad(x, self._pad, mode='replicate') for x in inputs]

    def unpad(self, x):
        assert x.ndim == 4
        ht, wd = x.shape[-2:]
        c = [self._pad[2], ht-self._pad[3], self._pad[0], wd-self._pad[1]]
        return x[..., c[0]:c[1], c[2]:c[3]]

def forward_interpolate(flow):
    flow = flow.detach().cpu().numpy()
    dx, dy = flow[0], flow[1]

    ht, wd = dx.shape
    x0, y0 = np.meshgrid(np.arange(wd), np.arange(ht))

    x1 = x0 + dx
    y1 = y0 + dy
    
    x1 = x1.reshape(-1)
    y1 = y1.reshape(-1)
    dx = dx.reshape(-1)
    dy = dy.reshape(-1)

    valid = (x1 > 0) & (x1 < wd) & (y1 > 0) & (y1 < ht)
    x1 = x1[valid]
    y1 = y1[valid]
    dx = dx[valid]
    dy = dy[valid]

    flow_x = interpolate.griddata(
        (x1, y1), dx, (x0, y0), method='nearest', fill_value=0)

    flow_y = interpolate.griddata(
        (x1, y1), dy, (x0, y0), method='nearest', fill_value=0)

    flow = np.stack([flow_x, flow_y], axis=0)
    return torch.from_numpy(flow).float()


def bilinear_sampler(img, coords, mode='bilinear', mask=False):
    """ Wrapper for grid_sample, uses pixel coordinates """
    H, W = img.shape[-2:]
    xgrid, ygrid = coords.split([1,1], dim=-1)
    xgrid = 2*xgrid/(W-1) - 1
    assert torch.unique(ygrid).numel() == 1 and H == 1 # This is a stereo problem
    grid = torch.cat([xgrid, ygrid], dim=-1)
    img = F.grid_sample(img, grid, align_corners=True)
    if mask:
        mask = (xgrid > -1) & (ygrid > -1) & (xgrid < 1) & (ygrid < 1)
        return img, mask.float()
    return img


def coords_grid(batch, ht, wd):
    coords = torch.meshgrid(torch.arange(ht), torch.arange(wd))
    coords = torch.stack(coords[::-1], dim=0).float()
    return coords[None].repeat(batch, 1, 1, 1)


def upflow8(flow, mode='bilinear'):
    new_size = (8 * flow.shape[2], 8 * flow.shape[3])
    return  8 * F.interpolate(flow, size=new_size, mode=mode, align_corners=True)

def gauss_blur(input, N=5, std=1):
    B, D, H, W = input.shape
    x, y = torch.meshgrid(torch.arange(N).float() - N//2, torch.arange(N).float() - N//2)
    unnormalized_gaussian = torch.exp(-(x.pow(2) + y.pow(2)) / (2 * std ** 2))
    weights = unnormalized_gaussian / unnormalized_gaussian.sum().clamp(min=1e-4)
    weights = weights.view(1,1,N,N).to(input)
    output = F.conv2d(input.reshape(B*D,1,H,W), weights, padding=N//2)
    return output.view(B, D, H, W)


# Monster Plus utility functions
def pool2x(x):
    """2x downsampling with average pooling"""
    return F.avg_pool2d(x, 3, stride=2, padding=1)


def interp(x, dest):
    """Bilinear interpolation to match destination shape"""
    return F.interpolate(x, dest.shape[2:], mode='bilinear', align_corners=True)


def detect_occlusion_torch(disp: torch.Tensor) -> tuple[torch.Tensor, torch.Tensor]:
    """
    使用 PyTorch 高效实现单调性检查，支持批量处理 (B, 1, H, W)。

    参数:
    - disp (torch.Tensor): 左视差图张量，形状为 (B, 1, H, W)。

    返回:
    - occlusion_mask (torch.Tensor): 遮挡掩码图 (torch.bool)，形状为 (B, 1, H, W)。True表示遮挡。
    """

    if disp.dim() == 3:
        disp = disp.unsqueeze(0)

    if not isinstance(disp, torch.Tensor) or disp.dim() != 4:
        raise ValueError("输入必须是一个形状为 (B, 1, H, W) 的 PyTorch 张量。")

    B, C, H, W = disp.shape
    device = disp.device
    dtype = disp.dtype

    # 1. 预处理：计算valid掩码和投影坐标xr
    valid = (disp > 0) & (disp < 1000)

    # 创建x坐标网格，并调整形状以支持广播 (1, 1, 1, W)
    x_coords = torch.arange(W, device=device, dtype=dtype).view(1, 1, 1, W)
    
    # 计算投影坐标 xr = x - disp(x)
    xr = x_coords - disp
    
    # 对于无效点，将其xr设置为无穷大，以避免影响累积最小值的计算
    # 使用 masked_fill_ 进行原地操作以节省内存
    xr.masked_fill_(~valid, float('inf'))

    # 2. 核心计算：沿宽度维度(dim=3)从右到左的累积最小值
    # a. 沿宽度维度(dim=3)翻转张量
    xr_flipped = torch.flip(xr, dims=[3])
    # b. 计算累积最小值。torch.cummin返回 (values, indices) 元组，我们只需要值
    cummin_values, _ = torch.cummin(xr_flipped, dim=3)
    # c. 再次翻转，得到从右到左的累积最小值
    xr_cummin_rtl = torch.flip(cummin_values, dims=[3])

    # 3. 识别遮挡像素
    # 条件1: 原始xr值大于其右侧的最小值 (向左扩展的遮挡)
    occlusion_smear = (xr > xr_cummin_rtl)

    # 条件2: 识别初始断点 (xr[i] < xr[i-1])
    # torch.diff 沿宽度维度(dim=3)计算差值
    initial_violations = torch.diff(xr, dim=3) < 0
    # 在左侧补上一列False，使其与原图同维度
    # F.pad的填充参数格式是 (左填充, 右填充, 上填充, 下填充, ...)
    initial_violations = F.pad(initial_violations, (1, 0, 0, 0, 0, 0, 0, 0), 'constant', 0)

    # 4. 合并并最终化
    # 最终的遮挡是两种情况的并集
    occlusion_mask = occlusion_smear | initial_violations
    
    # 确保只有valid区域内的像素可以被标记为遮挡
    occlusion_mask = occlusion_mask & valid

    x_coords = torch.arange(W, device=disp.device).view(1, 1, 1, W).expand(B, C, H, W)
    left_occlusion = (x_coords - disp < 0).to(occlusion_mask.dtype)
    # 合并左侧遮挡区域
    occlusion_mask = torch.logical_or(occlusion_mask.bool(), left_occlusion.bool()).to(occlusion_mask.dtype)

    return occlusion_mask




def detect_occlusion_numpy(disp: np.ndarray) -> np.ndarray:
    """
    使用 NumPy 高效实现单调性和边界遮挡检测。
    这是您提供的 PyTorch 函数的 NumPy (H, W) 版本。

    参数:
    - disp (np.ndarray): 左视差图，形状为 (H, W)。
    - min_disp (float): 有效的最小视差值。
    - max_disp (float): 有效的最大视差值。

    返回:
    - occlusion_mask (np.ndarray): 遮挡掩码图 (bool)，形状为 (H, W)。True表示遮挡。
    """
    if not isinstance(disp, np.ndarray) or disp.ndim != 2:
        raise ValueError("输入必须是一个形状为 (H, W) 的 NumPy 数组。")

    H, W = disp.shape

    # 1. 预处理：计算valid掩码和投影坐标xr
    valid = (disp > 0) & (disp < 1000)
    
    # 创建x坐标网格 (1, W)，以便与 (H, W) 的数组进行广播
    x_coords = np.arange(W, dtype=disp.dtype).reshape(1, W)
    
    # 计算投影坐标 xr = x - disp(x)
    xr = x_coords - disp
    
    # 对于无效点，将其xr设置为无穷大，以避免影响累积最小值的计算
    xr[~valid] = np.inf

    # 2. 核心计算：沿行(axis=1)从右到左的累积最小值
    # a. 沿行翻转数组 (left-right)
    xr_flipped = np.fliplr(xr)
    # b. 计算从左到右的累积最小值
    cummin_flipped = np.minimum.accumulate(xr_flipped, axis=1)
    # c. 再次翻转，得到从右到左的累积最小值
    xr_cummin_rtl = np.fliplr(cummin_flipped)

    # 3. 识别遮挡像素
    # 条件1: 原始xr值大于其右侧的最小值 (向左扩展的遮挡)
    occlusion_smear = (xr > xr_cummin_rtl)

    # 条件2: 识别初始断点 (xr[i] < xr[i-1])
    # np.diff 沿行(axis=1)计算差值
    initial_violations = np.diff(xr, axis=1) < 0
    # 在左侧补上一列False，使其与原图同维度
    initial_violations = np.pad(initial_violations, ((0, 0), (1, 0)), 'constant', constant_values=False)

    # 4. 合并并最终化
    # 组合由单调性产生的遮挡
    monotonicity_occlusion = occlusion_smear | initial_violations
    
    # 确保只有valid区域内的像素可以被标记
    monotonicity_occlusion &= valid
    
    # 新增条件：检查因视差过大导致的左侧边界遮挡
    left_border_occlusion = (x_coords - disp < 0)
    
    # 合并两种主要的遮挡来源
    occlusion_mask = np.logical_or(monotonicity_occlusion, left_border_occlusion)

    return occlusion_mask