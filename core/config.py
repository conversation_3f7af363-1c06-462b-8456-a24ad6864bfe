"""
Monster Plus 模型配置
"""

class ModelConfig:
    """Model configuration constants"""
    # Vision Transformer configurations
    INTERMEDIATE_LAYER_IDX = {
        'vits': [2, 5, 8, 11],
        'vitb': [2, 5, 8, 11], 
        'vitl': [4, 11, 17, 23], 
        'vitg': [9, 19, 29, 39]
    }
    
    MONO_MODEL_CONFIGS = {
        'vits': {'encoder': 'vits', 'features': 64, 'out_channels': [48, 96, 192, 384]},
        'vitb': {'encoder': 'vitb', 'features': 128, 'out_channels': [96, 192, 384, 768]},
        'vitl': {'encoder': 'vitl', 'features': 256, 'out_channels': [256, 512, 1024, 1024]},
        'vitg': {'encoder': 'vitg', 'features': 384, 'out_channels': [1536, 1536, 1536, 1536]}
    }
    
    # Feature enhancement settings
    FEATURE_DIM = 96
    NUM_ATTENTION_LAYERS = 2
    NUM_ATTENTION_HEADS = 4
    ROPE_FREQUENCY = 100
    NUM_REGISTER_TOKENS = 4  # Number of register tokens for attention enhancement
    
    # Attention weights
    FRAME_ALPHA_INIT = 0.1
    EPIPOLAR_ALPHA_INIT = 0.1
    GLOBAL_ALPHA_INIT = 0.2 